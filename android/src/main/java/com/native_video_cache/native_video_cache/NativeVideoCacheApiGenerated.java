// Autogenerated from <PERSON><PERSON> (v17.3.0), do not edit directly.
// See also: https://pub.dev/packages/pigeon

package com.native_video_cache.native_video_cache;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.CLASS;

import android.util.Log;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import io.flutter.plugin.common.BasicMessageChannel;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MessageCodec;
import io.flutter.plugin.common.StandardMessageCodec;
import java.io.ByteArrayOutputStream;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/** Generated class from Pigeon. */
@SuppressWarnings({"unused", "unchecked", "CodeBlock2Expr", "RedundantSuppression", "serial"})
public class NativeVideoCacheApiGenerated {

  /** Error class for passing custom error details to Flutter via a thrown PlatformException. */
  public static class FlutterError extends RuntimeException {

    /** The error code. */
    public final String code;

    /** The error details. Must be a datatype supported by the api codec. */
    public final Object details;

    public FlutterError(@NonNull String code, @Nullable String message, @Nullable Object details) 
    {
      super(message);
      this.code = code;
      this.details = details;
    }
  }

  @NonNull
  protected static ArrayList<Object> wrapError(@NonNull Throwable exception) {
    ArrayList<Object> errorList = new ArrayList<Object>(3);
    if (exception instanceof FlutterError) {
      FlutterError error = (FlutterError) exception;
      errorList.add(error.code);
      errorList.add(error.getMessage());
      errorList.add(error.details);
    } else {
      errorList.add(exception.toString());
      errorList.add(exception.getClass().getSimpleName());
      errorList.add(
        "Cause: " + exception.getCause() + ", Stacktrace: " + Log.getStackTraceString(exception));
    }
    return errorList;
  }

  @Target(METHOD)
  @Retention(CLASS)
  @interface CanIgnoreReturnValue {}

  /** 缓存状态枚举 */
  public enum CacheStatus {
    /** 未缓存 */
    NONE(0),
    /** 缓存中 */
    CACHING(1),
    /** 已缓存 */
    CACHED(2),
    /** 缓存错误 */
    ERROR(3);

    final int index;

    private CacheStatus(final int index) {
      this.index = index;
    }
  }

  /**
   * 缓存配置类
   *
   * Generated class from Pigeon that represents data sent in messages.
   */
  public static final class CacheConfig {
    /** 最大缓存大小 (bytes) */
    private @Nullable Long maxCacheSize;

    public @Nullable Long getMaxCacheSize() {
      return maxCacheSize;
    }

    public void setMaxCacheSize(@Nullable Long setterArg) {
      this.maxCacheSize = setterArg;
    }

    /** 缓存目录路径 */
    private @Nullable String cacheDirectory;

    public @Nullable String getCacheDirectory() {
      return cacheDirectory;
    }

    public void setCacheDirectory(@Nullable String setterArg) {
      this.cacheDirectory = setterArg;
    }

    /** 最大并发下载数 */
    private @Nullable Long maxConcurrentDownloads;

    public @Nullable Long getMaxConcurrentDownloads() {
      return maxConcurrentDownloads;
    }

    public void setMaxConcurrentDownloads(@Nullable Long setterArg) {
      this.maxConcurrentDownloads = setterArg;
    }

    /** 连接超时时间 (milliseconds) */
    private @Nullable Long connectTimeout;

    public @Nullable Long getConnectTimeout() {
      return connectTimeout;
    }

    public void setConnectTimeout(@Nullable Long setterArg) {
      this.connectTimeout = setterArg;
    }

    /** 读取超时时间 (milliseconds) */
    private @Nullable Long readTimeout;

    public @Nullable Long getReadTimeout() {
      return readTimeout;
    }

    public void setReadTimeout(@Nullable Long setterArg) {
      this.readTimeout = setterArg;
    }

    public static final class Builder {

      private @Nullable Long maxCacheSize;

      @CanIgnoreReturnValue
      public @NonNull Builder setMaxCacheSize(@Nullable Long setterArg) {
        this.maxCacheSize = setterArg;
        return this;
      }

      private @Nullable String cacheDirectory;

      @CanIgnoreReturnValue
      public @NonNull Builder setCacheDirectory(@Nullable String setterArg) {
        this.cacheDirectory = setterArg;
        return this;
      }

      private @Nullable Long maxConcurrentDownloads;

      @CanIgnoreReturnValue
      public @NonNull Builder setMaxConcurrentDownloads(@Nullable Long setterArg) {
        this.maxConcurrentDownloads = setterArg;
        return this;
      }

      private @Nullable Long connectTimeout;

      @CanIgnoreReturnValue
      public @NonNull Builder setConnectTimeout(@Nullable Long setterArg) {
        this.connectTimeout = setterArg;
        return this;
      }

      private @Nullable Long readTimeout;

      @CanIgnoreReturnValue
      public @NonNull Builder setReadTimeout(@Nullable Long setterArg) {
        this.readTimeout = setterArg;
        return this;
      }

      public @NonNull CacheConfig build() {
        CacheConfig pigeonReturn = new CacheConfig();
        pigeonReturn.setMaxCacheSize(maxCacheSize);
        pigeonReturn.setCacheDirectory(cacheDirectory);
        pigeonReturn.setMaxConcurrentDownloads(maxConcurrentDownloads);
        pigeonReturn.setConnectTimeout(connectTimeout);
        pigeonReturn.setReadTimeout(readTimeout);
        return pigeonReturn;
      }
    }

    @NonNull
    ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(5);
      toListResult.add(maxCacheSize);
      toListResult.add(cacheDirectory);
      toListResult.add(maxConcurrentDownloads);
      toListResult.add(connectTimeout);
      toListResult.add(readTimeout);
      return toListResult;
    }

    static @NonNull CacheConfig fromList(@NonNull ArrayList<Object> list) {
      CacheConfig pigeonResult = new CacheConfig();
      Object maxCacheSize = list.get(0);
      pigeonResult.setMaxCacheSize((maxCacheSize == null) ? null : ((maxCacheSize instanceof Integer) ? (Integer) maxCacheSize : (Long) maxCacheSize));
      Object cacheDirectory = list.get(1);
      pigeonResult.setCacheDirectory((String) cacheDirectory);
      Object maxConcurrentDownloads = list.get(2);
      pigeonResult.setMaxConcurrentDownloads((maxConcurrentDownloads == null) ? null : ((maxConcurrentDownloads instanceof Integer) ? (Integer) maxConcurrentDownloads : (Long) maxConcurrentDownloads));
      Object connectTimeout = list.get(3);
      pigeonResult.setConnectTimeout((connectTimeout == null) ? null : ((connectTimeout instanceof Integer) ? (Integer) connectTimeout : (Long) connectTimeout));
      Object readTimeout = list.get(4);
      pigeonResult.setReadTimeout((readTimeout == null) ? null : ((readTimeout instanceof Integer) ? (Integer) readTimeout : (Long) readTimeout));
      return pigeonResult;
    }
  }

  /**
   * 缓存信息类
   *
   * Generated class from Pigeon that represents data sent in messages.
   */
  public static final class CacheInfo {
    /** 原始URL */
    private @NonNull String originalUrl;

    public @NonNull String getOriginalUrl() {
      return originalUrl;
    }

    public void setOriginalUrl(@NonNull String setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"originalUrl\" is null.");
      }
      this.originalUrl = setterArg;
    }

    /** 缓存状态 */
    private @NonNull CacheStatus status;

    public @NonNull CacheStatus getStatus() {
      return status;
    }

    public void setStatus(@NonNull CacheStatus setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"status\" is null.");
      }
      this.status = setterArg;
    }

    /** 缓存进度 (0.0 - 1.0) */
    private @NonNull Double progress;

    public @NonNull Double getProgress() {
      return progress;
    }

    public void setProgress(@NonNull Double setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"progress\" is null.");
      }
      this.progress = setterArg;
    }

    /** 已缓存大小 (bytes) */
    private @NonNull Long cachedSize;

    public @NonNull Long getCachedSize() {
      return cachedSize;
    }

    public void setCachedSize(@NonNull Long setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"cachedSize\" is null.");
      }
      this.cachedSize = setterArg;
    }

    /** 总大小 (bytes) */
    private @NonNull Long totalSize;

    public @NonNull Long getTotalSize() {
      return totalSize;
    }

    public void setTotalSize(@NonNull Long setterArg) {
      if (setterArg == null) {
        throw new IllegalStateException("Nonnull field \"totalSize\" is null.");
      }
      this.totalSize = setterArg;
    }

    /** 错误信息 */
    private @Nullable String errorMessage;

    public @Nullable String getErrorMessage() {
      return errorMessage;
    }

    public void setErrorMessage(@Nullable String setterArg) {
      this.errorMessage = setterArg;
    }

    /** Constructor is non-public to enforce null safety; use Builder. */
    CacheInfo() {}

    public static final class Builder {

      private @Nullable String originalUrl;

      @CanIgnoreReturnValue
      public @NonNull Builder setOriginalUrl(@NonNull String setterArg) {
        this.originalUrl = setterArg;
        return this;
      }

      private @Nullable CacheStatus status;

      @CanIgnoreReturnValue
      public @NonNull Builder setStatus(@NonNull CacheStatus setterArg) {
        this.status = setterArg;
        return this;
      }

      private @Nullable Double progress;

      @CanIgnoreReturnValue
      public @NonNull Builder setProgress(@NonNull Double setterArg) {
        this.progress = setterArg;
        return this;
      }

      private @Nullable Long cachedSize;

      @CanIgnoreReturnValue
      public @NonNull Builder setCachedSize(@NonNull Long setterArg) {
        this.cachedSize = setterArg;
        return this;
      }

      private @Nullable Long totalSize;

      @CanIgnoreReturnValue
      public @NonNull Builder setTotalSize(@NonNull Long setterArg) {
        this.totalSize = setterArg;
        return this;
      }

      private @Nullable String errorMessage;

      @CanIgnoreReturnValue
      public @NonNull Builder setErrorMessage(@Nullable String setterArg) {
        this.errorMessage = setterArg;
        return this;
      }

      public @NonNull CacheInfo build() {
        CacheInfo pigeonReturn = new CacheInfo();
        pigeonReturn.setOriginalUrl(originalUrl);
        pigeonReturn.setStatus(status);
        pigeonReturn.setProgress(progress);
        pigeonReturn.setCachedSize(cachedSize);
        pigeonReturn.setTotalSize(totalSize);
        pigeonReturn.setErrorMessage(errorMessage);
        return pigeonReturn;
      }
    }

    @NonNull
    ArrayList<Object> toList() {
      ArrayList<Object> toListResult = new ArrayList<Object>(6);
      toListResult.add(originalUrl);
      toListResult.add(status == null ? null : status.index);
      toListResult.add(progress);
      toListResult.add(cachedSize);
      toListResult.add(totalSize);
      toListResult.add(errorMessage);
      return toListResult;
    }

    static @NonNull CacheInfo fromList(@NonNull ArrayList<Object> list) {
      CacheInfo pigeonResult = new CacheInfo();
      Object originalUrl = list.get(0);
      pigeonResult.setOriginalUrl((String) originalUrl);
      Object status = list.get(1);
      pigeonResult.setStatus(CacheStatus.values()[(int) status]);
      Object progress = list.get(2);
      pigeonResult.setProgress((Double) progress);
      Object cachedSize = list.get(3);
      pigeonResult.setCachedSize((cachedSize == null) ? null : ((cachedSize instanceof Integer) ? (Integer) cachedSize : (Long) cachedSize));
      Object totalSize = list.get(4);
      pigeonResult.setTotalSize((totalSize == null) ? null : ((totalSize instanceof Integer) ? (Integer) totalSize : (Long) totalSize));
      Object errorMessage = list.get(5);
      pigeonResult.setErrorMessage((String) errorMessage);
      return pigeonResult;
    }
  }

  /** Asynchronous error handling return type for non-nullable API method returns. */
  public interface Result<T> {
    /** Success case callback method for handling returns. */
    void success(@NonNull T result);

    /** Failure case callback method for handling errors. */
    void error(@NonNull Throwable error);
  }
  /** Asynchronous error handling return type for nullable API method returns. */
  public interface NullableResult<T> {
    /** Success case callback method for handling returns. */
    void success(@Nullable T result);

    /** Failure case callback method for handling errors. */
    void error(@NonNull Throwable error);
  }
  /** Asynchronous error handling return type for void API method returns. */
  public interface VoidResult {
    /** Success case callback method for handling returns. */
    void success();

    /** Failure case callback method for handling errors. */
    void error(@NonNull Throwable error);
  }
  /**
   * 缓存监听回调
   *
   * Generated interface from Pigeon that represents a handler of messages from Flutter.
   */
  public interface NativeVideoCacheCacheListener {
    /** 缓存进度变化回调 */
    void onCacheProgressChanged(@NonNull String url, @NonNull Double progress);
    /** 缓存状态变化回调 */
    void onCacheStatusChanged(@NonNull String url, @NonNull CacheStatus status);
    /** 缓存错误回调 */
    void onCacheError(@NonNull String url, @NonNull String error);

    /** The codec used by NativeVideoCacheCacheListener. */
    static @NonNull MessageCodec<Object> getCodec() {
      return new StandardMessageCodec();
    }
    /**Sets up an instance of `NativeVideoCacheCacheListener` to handle messages through the `binaryMessenger`. */
    static void setUp(@NonNull BinaryMessenger binaryMessenger, @Nullable NativeVideoCacheCacheListener api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheCacheListener.onCacheProgressChanged", getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                String urlArg = (String) args.get(0);
                Double progressArg = (Double) args.get(1);
                try {
                  api.onCacheProgressChanged(urlArg, progressArg);
                  wrapped.add(0, null);
                }
 catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheCacheListener.onCacheStatusChanged", getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                String urlArg = (String) args.get(0);
                CacheStatus statusArg = CacheStatus.values()[(int) args.get(1)];
                try {
                  api.onCacheStatusChanged(urlArg, statusArg);
                  wrapped.add(0, null);
                }
 catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheCacheListener.onCacheError", getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                String urlArg = (String) args.get(0);
                String errorArg = (String) args.get(1);
                try {
                  api.onCacheError(urlArg, errorArg);
                  wrapped.add(0, null);
                }
 catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }

  private static class NativeVideoCacheApiCodec extends StandardMessageCodec {
    public static final NativeVideoCacheApiCodec INSTANCE = new NativeVideoCacheApiCodec();

    private NativeVideoCacheApiCodec() {}

    @Override
    protected Object readValueOfType(byte type, @NonNull ByteBuffer buffer) {
      switch (type) {
        case (byte) 128:
          return CacheConfig.fromList((ArrayList<Object>) readValue(buffer));
        case (byte) 129:
          return CacheInfo.fromList((ArrayList<Object>) readValue(buffer));
        default:
          return super.readValueOfType(type, buffer);
      }
    }

    @Override
    protected void writeValue(@NonNull ByteArrayOutputStream stream, Object value) {
      if (value instanceof CacheConfig) {
        stream.write(128);
        writeValue(stream, ((CacheConfig) value).toList());
      } else if (value instanceof CacheInfo) {
        stream.write(129);
        writeValue(stream, ((CacheInfo) value).toList());
      } else {
        super.writeValue(stream, value);
      }
    }
  }

  /**
   * Native Video Cache API
   *
   * Generated interface from Pigeon that represents a handler of messages from Flutter.
   */
  public interface NativeVideoCacheApi {
    /** 初始化缓存系统 */
    void initialize(@NonNull CacheConfig config, @NonNull VoidResult result);
    /** 获取代理URL (用于播放器) */
    void getProxyUrl(@NonNull String originalUrl, @NonNull Result<String> result);
    /** 开始缓存视频 */
    void startCache(@NonNull String url, @NonNull VoidResult result);
    /** 停止缓存视频 */
    void stopCache(@NonNull String url, @NonNull VoidResult result);
    /** 获取缓存信息 */
    void getCacheInfo(@NonNull String url, @NonNull Result<CacheInfo> result);
    /** 清理所有缓存 */
    void clearAllCache(@NonNull VoidResult result);
    /** 清理指定URL的缓存 */
    void clearCache(@NonNull String url, @NonNull VoidResult result);
    /** 获取总缓存大小 */
    void getTotalCacheSize(@NonNull Result<Long> result);
    /** 检查是否已缓存 */
    void isCached(@NonNull String url, @NonNull Result<Boolean> result);
    /** 设置缓存监听器 */
    void setCacheListener();
    /** 移除缓存监听器 */
    void removeCacheListener();

    /** The codec used by NativeVideoCacheApi. */
    static @NonNull MessageCodec<Object> getCodec() {
      return NativeVideoCacheApiCodec.INSTANCE;
    }
    /**Sets up an instance of `NativeVideoCacheApi` to handle messages through the `binaryMessenger`. */
    static void setUp(@NonNull BinaryMessenger binaryMessenger, @Nullable NativeVideoCacheApi api) {
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.initialize", getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                CacheConfig configArg = (CacheConfig) args.get(0);
                VoidResult resultCallback =
                    new VoidResult() {
                      public void success() {
                        wrapped.add(0, null);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.initialize(configArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.getProxyUrl", getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                String originalUrlArg = (String) args.get(0);
                Result<String> resultCallback =
                    new Result<String>() {
                      public void success(String result) {
                        wrapped.add(0, result);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.getProxyUrl(originalUrlArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.startCache", getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                String urlArg = (String) args.get(0);
                VoidResult resultCallback =
                    new VoidResult() {
                      public void success() {
                        wrapped.add(0, null);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.startCache(urlArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.stopCache", getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                String urlArg = (String) args.get(0);
                VoidResult resultCallback =
                    new VoidResult() {
                      public void success() {
                        wrapped.add(0, null);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.stopCache(urlArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.getCacheInfo", getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                String urlArg = (String) args.get(0);
                Result<CacheInfo> resultCallback =
                    new Result<CacheInfo>() {
                      public void success(CacheInfo result) {
                        wrapped.add(0, result);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.getCacheInfo(urlArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.clearAllCache", getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                VoidResult resultCallback =
                    new VoidResult() {
                      public void success() {
                        wrapped.add(0, null);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.clearAllCache(resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.clearCache", getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                String urlArg = (String) args.get(0);
                VoidResult resultCallback =
                    new VoidResult() {
                      public void success() {
                        wrapped.add(0, null);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.clearCache(urlArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.getTotalCacheSize", getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                Result<Long> resultCallback =
                    new Result<Long>() {
                      public void success(Long result) {
                        wrapped.add(0, result);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.getTotalCacheSize(resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.isCached", getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                ArrayList<Object> args = (ArrayList<Object>) message;
                String urlArg = (String) args.get(0);
                Result<Boolean> resultCallback =
                    new Result<Boolean>() {
                      public void success(Boolean result) {
                        wrapped.add(0, result);
                        reply.reply(wrapped);
                      }

                      public void error(Throwable error) {
                        ArrayList<Object> wrappedError = wrapError(error);
                        reply.reply(wrappedError);
                      }
                    };

                api.isCached(urlArg, resultCallback);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.setCacheListener", getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                try {
                  api.setCacheListener();
                  wrapped.add(0, null);
                }
 catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
      {
        BasicMessageChannel<Object> channel =
            new BasicMessageChannel<>(
                binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.removeCacheListener", getCodec());
        if (api != null) {
          channel.setMessageHandler(
              (message, reply) -> {
                ArrayList<Object> wrapped = new ArrayList<Object>();
                try {
                  api.removeCacheListener();
                  wrapped.add(0, null);
                }
 catch (Throwable exception) {
                  ArrayList<Object> wrappedError = wrapError(exception);
                  wrapped = wrappedError;
                }
                reply.reply(wrapped);
              });
        } else {
          channel.setMessageHandler(null);
        }
      }
    }
  }
}
