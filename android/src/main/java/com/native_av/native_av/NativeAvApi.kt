// Autogenerated from <PERSON><PERSON> (v17.3.0), do not edit directly.
// See also: https://pub.dev/packages/pigeon

package com.native_av.native_av

import android.util.Log
import io.flutter.plugin.common.BasicMessageChannel
import io.flutter.plugin.common.BinaryMessenger
import io.flutter.plugin.common.MessageCodec
import io.flutter.plugin.common.StandardMessageCodec
import java.io.ByteArrayOutputStream
import java.nio.ByteBuffer

private fun wrapResult(result: Any?): List<Any?> {
  return listOf(result)
}

private fun wrapError(exception: Throwable): List<Any?> {
  if (exception is FlutterError) {
    return listOf(
      exception.code,
      exception.message,
      exception.details
    )
  } else {
    return listOf(
      exception.javaClass.simpleName,
      exception.toString(),
      "Cause: " + exception.cause + ", Stacktrace: " + Log.getStackTraceString(exception)
    )
  }
}

/**
 * Error class for passing custom error details to Flutter via a thrown PlatformException.
 * @property code The error code.
 * @property message The error message.
 * @property details The error details. Must be a datatype supported by the api codec.
 */
class FlutterError (
  val code: String,
  override val message: String? = null,
  val details: Any? = null
) : Throwable()

/** 缓存状态枚举 */
enum class CacheStatus(val raw: Int) {
  /** 未缓存 */
  NONE(0),
  /** 缓存中 */
  CACHING(1),
  /** 已缓存 */
  CACHED(2),
  /** 缓存错误 */
  ERROR(3);

  companion object {
    fun ofRaw(raw: Int): CacheStatus? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

/**
 * 缓存配置类
 *
 * Generated class from Pigeon that represents data sent in messages.
 */
data class CacheConfig (
  /** 最大缓存大小 (bytes) */
  val maxCacheSize: Long? = null,
  /** 缓存目录路径 */
  val cacheDirectory: String? = null,
  /** 最大并发下载数 */
  val maxConcurrentDownloads: Long? = null,
  /** 连接超时时间 (milliseconds) */
  val connectTimeout: Long? = null,
  /** 读取超时时间 (milliseconds) */
  val readTimeout: Long? = null

) {
  companion object {
    @Suppress("UNCHECKED_CAST")
    fun fromList(list: List<Any?>): CacheConfig {
      val maxCacheSize = list[0].let { if (it is Int) it.toLong() else it as Long? }
      val cacheDirectory = list[1] as String?
      val maxConcurrentDownloads = list[2].let { if (it is Int) it.toLong() else it as Long? }
      val connectTimeout = list[3].let { if (it is Int) it.toLong() else it as Long? }
      val readTimeout = list[4].let { if (it is Int) it.toLong() else it as Long? }
      return CacheConfig(maxCacheSize, cacheDirectory, maxConcurrentDownloads, connectTimeout, readTimeout)
    }
  }
  fun toList(): List<Any?> {
    return listOf<Any?>(
      maxCacheSize,
      cacheDirectory,
      maxConcurrentDownloads,
      connectTimeout,
      readTimeout,
    )
  }
}

/**
 * 缓存信息类
 *
 * Generated class from Pigeon that represents data sent in messages.
 */
data class CacheInfo (
  /** 原始URL */
  val originalUrl: String,
  /** 缓存状态 */
  val status: CacheStatus,
  /** 缓存进度 (0.0 - 1.0) */
  val progress: Double,
  /** 已缓存大小 (bytes) */
  val cachedSize: Long,
  /** 总大小 (bytes) */
  val totalSize: Long,
  /** 错误信息 */
  val errorMessage: String? = null

) {
  companion object {
    @Suppress("UNCHECKED_CAST")
    fun fromList(list: List<Any?>): CacheInfo {
      val originalUrl = list[0] as String
      val status = CacheStatus.ofRaw(list[1] as Int)!!
      val progress = list[2] as Double
      val cachedSize = list[3].let { if (it is Int) it.toLong() else it as Long }
      val totalSize = list[4].let { if (it is Int) it.toLong() else it as Long }
      val errorMessage = list[5] as String?
      return CacheInfo(originalUrl, status, progress, cachedSize, totalSize, errorMessage)
    }
  }
  fun toList(): List<Any?> {
    return listOf<Any?>(
      originalUrl,
      status.raw,
      progress,
      cachedSize,
      totalSize,
      errorMessage,
    )
  }
}

/**
 * 缓存监听回调
 *
 * Generated interface from Pigeon that represents a handler of messages from Flutter.
 */
interface NativeAvCacheListener {
  /** 缓存进度变化回调 */
  fun onCacheProgressChanged(url: String, progress: Double)
  /** 缓存状态变化回调 */
  fun onCacheStatusChanged(url: String, status: CacheStatus)
  /** 缓存错误回调 */
  fun onCacheError(url: String, error: String)

  companion object {
    /** The codec used by NativeAvCacheListener. */
    val codec: MessageCodec<Any?> by lazy {
      StandardMessageCodec()
    }
    /** Sets up an instance of `NativeAvCacheListener` to handle messages through the `binaryMessenger`. */
    @Suppress("UNCHECKED_CAST")
    fun setUp(binaryMessenger: BinaryMessenger, api: NativeAvCacheListener?) {
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeAvCacheListener.onCacheProgressChanged", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val urlArg = args[0] as String
            val progressArg = args[1] as Double
            var wrapped: List<Any?>
            try {
              api.onCacheProgressChanged(urlArg, progressArg)
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeAvCacheListener.onCacheStatusChanged", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val urlArg = args[0] as String
            val statusArg = CacheStatus.ofRaw(args[1] as Int)!!
            var wrapped: List<Any?>
            try {
              api.onCacheStatusChanged(urlArg, statusArg)
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeAvCacheListener.onCacheError", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val urlArg = args[0] as String
            val errorArg = args[1] as String
            var wrapped: List<Any?>
            try {
              api.onCacheError(urlArg, errorArg)
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }
}
@Suppress("UNCHECKED_CAST")
private object NativeAvApiCodec : StandardMessageCodec() {
  override fun readValueOfType(type: Byte, buffer: ByteBuffer): Any? {
    return when (type) {
      128.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          CacheConfig.fromList(it)
        }
      }
      129.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          CacheInfo.fromList(it)
        }
      }
      else -> super.readValueOfType(type, buffer)
    }
  }
  override fun writeValue(stream: ByteArrayOutputStream, value: Any?)   {
    when (value) {
      is CacheConfig -> {
        stream.write(128)
        writeValue(stream, value.toList())
      }
      is CacheInfo -> {
        stream.write(129)
        writeValue(stream, value.toList())
      }
      else -> super.writeValue(stream, value)
    }
  }
}

/**
 * Native Video Cache API
 *
 * Generated interface from Pigeon that represents a handler of messages from Flutter.
 */
interface NativeAvApi {
  /** 初始化缓存系统 */
  fun initialize(config: CacheConfig, callback: (Result<Unit>) -> Unit)
  /** 获取代理URL (用于播放器) */
  fun getProxyUrl(originalUrl: String, callback: (Result<String>) -> Unit)
  /** 开始缓存视频 */
  fun startCache(url: String, callback: (Result<Unit>) -> Unit)
  /** 停止缓存视频 */
  fun stopCache(url: String, callback: (Result<Unit>) -> Unit)
  /** 获取缓存信息 */
  fun getCacheInfo(url: String, callback: (Result<CacheInfo>) -> Unit)
  /** 清理所有缓存 */
  fun clearAllCache(callback: (Result<Unit>) -> Unit)
  /** 清理指定URL的缓存 */
  fun clearCache(url: String, callback: (Result<Unit>) -> Unit)
  /** 获取总缓存大小 */
  fun getTotalCacheSize(callback: (Result<Long>) -> Unit)
  /** 检查是否已缓存 */
  fun isCached(url: String, callback: (Result<Boolean>) -> Unit)
  /** 设置缓存监听器 */
  fun setCacheListener()
  /** 移除缓存监听器 */
  fun removeCacheListener()

  companion object {
    /** The codec used by NativeAvApi. */
    val codec: MessageCodec<Any?> by lazy {
      NativeAvApiCodec
    }
    /** Sets up an instance of `NativeAvApi` to handle messages through the `binaryMessenger`. */
    @Suppress("UNCHECKED_CAST")
    fun setUp(binaryMessenger: BinaryMessenger, api: NativeAvApi?) {
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeAvApi.initialize", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val configArg = args[0] as CacheConfig
            api.initialize(configArg) { result: Result<Unit> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                reply.reply(wrapResult(null))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeAvApi.getProxyUrl", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val originalUrlArg = args[0] as String
            api.getProxyUrl(originalUrlArg) { result: Result<String> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeAvApi.startCache", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val urlArg = args[0] as String
            api.startCache(urlArg) { result: Result<Unit> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                reply.reply(wrapResult(null))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeAvApi.stopCache", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val urlArg = args[0] as String
            api.stopCache(urlArg) { result: Result<Unit> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                reply.reply(wrapResult(null))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeAvApi.getCacheInfo", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val urlArg = args[0] as String
            api.getCacheInfo(urlArg) { result: Result<CacheInfo> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeAvApi.clearAllCache", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            api.clearAllCache() { result: Result<Unit> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                reply.reply(wrapResult(null))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeAvApi.clearCache", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val urlArg = args[0] as String
            api.clearCache(urlArg) { result: Result<Unit> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                reply.reply(wrapResult(null))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeAvApi.getTotalCacheSize", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            api.getTotalCacheSize() { result: Result<Long> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeAvApi.isCached", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val urlArg = args[0] as String
            api.isCached(urlArg) { result: Result<Boolean> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeAvApi.setCacheListener", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            var wrapped: List<Any?>
            try {
              api.setCacheListener()
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.native_video_cache.NativeAvApi.removeCacheListener", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            var wrapped: List<Any?>
            try {
              api.removeCacheListener()
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }
}
