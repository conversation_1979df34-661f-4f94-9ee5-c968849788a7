// Autogenerated from <PERSON><PERSON> (v17.3.0), do not edit directly.
// See also: https://pub.dev/packages/pigeon

#undef _HAS_EXCEPTIONS

#include "native_video_cache_api.h"

#include <flutter/basic_message_channel.h>
#include <flutter/binary_messenger.h>
#include <flutter/encodable_value.h>
#include <flutter/standard_message_codec.h>

#include <map>
#include <optional>
#include <string>

namespace native_video_cache {
using flutter::BasicMessageChannel;
using flutter::CustomEncodableValue;
using flutter::EncodableList;
using flutter::EncodableMap;
using flutter::EncodableValue;

FlutterError CreateConnectionError(const std::string channel_name) {
    return FlutterError(
        "channel-error",
        "Unable to establish connection on channel: '" + channel_name + "'.",
        EncodableValue(""));
}

// CacheConfig

CacheConfig::CacheConfig() {}

CacheConfig::CacheConfig(
  const int64_t* max_cache_size,
  const std::string* cache_directory,
  const int64_t* max_concurrent_downloads,
  const int64_t* connect_timeout,
  const int64_t* read_timeout)
 : max_cache_size_(max_cache_size ? std::optional<int64_t>(*max_cache_size) : std::nullopt),
    cache_directory_(cache_directory ? std::optional<std::string>(*cache_directory) : std::nullopt),
    max_concurrent_downloads_(max_concurrent_downloads ? std::optional<int64_t>(*max_concurrent_downloads) : std::nullopt),
    connect_timeout_(connect_timeout ? std::optional<int64_t>(*connect_timeout) : std::nullopt),
    read_timeout_(read_timeout ? std::optional<int64_t>(*read_timeout) : std::nullopt) {}

const int64_t* CacheConfig::max_cache_size() const {
  return max_cache_size_ ? &(*max_cache_size_) : nullptr;
}

void CacheConfig::set_max_cache_size(const int64_t* value_arg) {
  max_cache_size_ = value_arg ? std::optional<int64_t>(*value_arg) : std::nullopt;
}

void CacheConfig::set_max_cache_size(int64_t value_arg) {
  max_cache_size_ = value_arg;
}


const std::string* CacheConfig::cache_directory() const {
  return cache_directory_ ? &(*cache_directory_) : nullptr;
}

void CacheConfig::set_cache_directory(const std::string_view* value_arg) {
  cache_directory_ = value_arg ? std::optional<std::string>(*value_arg) : std::nullopt;
}

void CacheConfig::set_cache_directory(std::string_view value_arg) {
  cache_directory_ = value_arg;
}


const int64_t* CacheConfig::max_concurrent_downloads() const {
  return max_concurrent_downloads_ ? &(*max_concurrent_downloads_) : nullptr;
}

void CacheConfig::set_max_concurrent_downloads(const int64_t* value_arg) {
  max_concurrent_downloads_ = value_arg ? std::optional<int64_t>(*value_arg) : std::nullopt;
}

void CacheConfig::set_max_concurrent_downloads(int64_t value_arg) {
  max_concurrent_downloads_ = value_arg;
}


const int64_t* CacheConfig::connect_timeout() const {
  return connect_timeout_ ? &(*connect_timeout_) : nullptr;
}

void CacheConfig::set_connect_timeout(const int64_t* value_arg) {
  connect_timeout_ = value_arg ? std::optional<int64_t>(*value_arg) : std::nullopt;
}

void CacheConfig::set_connect_timeout(int64_t value_arg) {
  connect_timeout_ = value_arg;
}


const int64_t* CacheConfig::read_timeout() const {
  return read_timeout_ ? &(*read_timeout_) : nullptr;
}

void CacheConfig::set_read_timeout(const int64_t* value_arg) {
  read_timeout_ = value_arg ? std::optional<int64_t>(*value_arg) : std::nullopt;
}

void CacheConfig::set_read_timeout(int64_t value_arg) {
  read_timeout_ = value_arg;
}


EncodableList CacheConfig::ToEncodableList() const {
  EncodableList list;
  list.reserve(5);
  list.push_back(max_cache_size_ ? EncodableValue(*max_cache_size_) : EncodableValue());
  list.push_back(cache_directory_ ? EncodableValue(*cache_directory_) : EncodableValue());
  list.push_back(max_concurrent_downloads_ ? EncodableValue(*max_concurrent_downloads_) : EncodableValue());
  list.push_back(connect_timeout_ ? EncodableValue(*connect_timeout_) : EncodableValue());
  list.push_back(read_timeout_ ? EncodableValue(*read_timeout_) : EncodableValue());
  return list;
}

CacheConfig CacheConfig::FromEncodableList(const EncodableList& list) {
  CacheConfig decoded;
  auto& encodable_max_cache_size = list[0];
  if (!encodable_max_cache_size.IsNull()) {
    decoded.set_max_cache_size(encodable_max_cache_size.LongValue());
  }
  auto& encodable_cache_directory = list[1];
  if (!encodable_cache_directory.IsNull()) {
    decoded.set_cache_directory(std::get<std::string>(encodable_cache_directory));
  }
  auto& encodable_max_concurrent_downloads = list[2];
  if (!encodable_max_concurrent_downloads.IsNull()) {
    decoded.set_max_concurrent_downloads(encodable_max_concurrent_downloads.LongValue());
  }
  auto& encodable_connect_timeout = list[3];
  if (!encodable_connect_timeout.IsNull()) {
    decoded.set_connect_timeout(encodable_connect_timeout.LongValue());
  }
  auto& encodable_read_timeout = list[4];
  if (!encodable_read_timeout.IsNull()) {
    decoded.set_read_timeout(encodable_read_timeout.LongValue());
  }
  return decoded;
}

// CacheInfo

CacheInfo::CacheInfo(
  const std::string& original_url,
  const CacheStatus& status,
  double progress,
  int64_t cached_size,
  int64_t total_size)
 : original_url_(original_url),
    status_(status),
    progress_(progress),
    cached_size_(cached_size),
    total_size_(total_size) {}

CacheInfo::CacheInfo(
  const std::string& original_url,
  const CacheStatus& status,
  double progress,
  int64_t cached_size,
  int64_t total_size,
  const std::string* error_message)
 : original_url_(original_url),
    status_(status),
    progress_(progress),
    cached_size_(cached_size),
    total_size_(total_size),
    error_message_(error_message ? std::optional<std::string>(*error_message) : std::nullopt) {}

const std::string& CacheInfo::original_url() const {
  return original_url_;
}

void CacheInfo::set_original_url(std::string_view value_arg) {
  original_url_ = value_arg;
}


const CacheStatus& CacheInfo::status() const {
  return status_;
}

void CacheInfo::set_status(const CacheStatus& value_arg) {
  status_ = value_arg;
}


double CacheInfo::progress() const {
  return progress_;
}

void CacheInfo::set_progress(double value_arg) {
  progress_ = value_arg;
}


int64_t CacheInfo::cached_size() const {
  return cached_size_;
}

void CacheInfo::set_cached_size(int64_t value_arg) {
  cached_size_ = value_arg;
}


int64_t CacheInfo::total_size() const {
  return total_size_;
}

void CacheInfo::set_total_size(int64_t value_arg) {
  total_size_ = value_arg;
}


const std::string* CacheInfo::error_message() const {
  return error_message_ ? &(*error_message_) : nullptr;
}

void CacheInfo::set_error_message(const std::string_view* value_arg) {
  error_message_ = value_arg ? std::optional<std::string>(*value_arg) : std::nullopt;
}

void CacheInfo::set_error_message(std::string_view value_arg) {
  error_message_ = value_arg;
}


EncodableList CacheInfo::ToEncodableList() const {
  EncodableList list;
  list.reserve(6);
  list.push_back(EncodableValue(original_url_));
  list.push_back(EncodableValue((int)status_));
  list.push_back(EncodableValue(progress_));
  list.push_back(EncodableValue(cached_size_));
  list.push_back(EncodableValue(total_size_));
  list.push_back(error_message_ ? EncodableValue(*error_message_) : EncodableValue());
  return list;
}

CacheInfo CacheInfo::FromEncodableList(const EncodableList& list) {
  CacheInfo decoded(
    std::get<std::string>(list[0]),
    (CacheStatus)(std::get<int32_t>(list[1])),
    std::get<double>(list[2]),
    list[3].LongValue(),
    list[4].LongValue());
  auto& encodable_error_message = list[5];
  if (!encodable_error_message.IsNull()) {
    decoded.set_error_message(std::get<std::string>(encodable_error_message));
  }
  return decoded;
}

/// The codec used by NativeVideoCacheCacheListener.
const flutter::StandardMessageCodec& NativeVideoCacheCacheListener::GetCodec() {
  return flutter::StandardMessageCodec::GetInstance(&flutter::StandardCodecSerializer::GetInstance());
}

// Sets up an instance of `NativeVideoCacheCacheListener` to handle messages through the `binary_messenger`.
void NativeVideoCacheCacheListener::SetUp(
  flutter::BinaryMessenger* binary_messenger,
  NativeVideoCacheCacheListener* api) {
  {
    BasicMessageChannel<> channel(binary_messenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheCacheListener.onCacheProgressChanged", &GetCodec());
    if (api != nullptr) {
      channel.SetMessageHandler([api](const EncodableValue& message, const flutter::MessageReply<EncodableValue>& reply) {
        try {
          const auto& args = std::get<EncodableList>(message);
          const auto& encodable_url_arg = args.at(0);
          if (encodable_url_arg.IsNull()) {
            reply(WrapError("url_arg unexpectedly null."));
            return;
          }
          const auto& url_arg = std::get<std::string>(encodable_url_arg);
          const auto& encodable_progress_arg = args.at(1);
          if (encodable_progress_arg.IsNull()) {
            reply(WrapError("progress_arg unexpectedly null."));
            return;
          }
          const auto& progress_arg = std::get<double>(encodable_progress_arg);
          std::optional<FlutterError> output = api->OnCacheProgressChanged(url_arg, progress_arg);
          if (output.has_value()) {
            reply(WrapError(output.value()));
            return;
          }
          EncodableList wrapped;
          wrapped.push_back(EncodableValue());
          reply(EncodableValue(std::move(wrapped)));
        } catch (const std::exception& exception) {
          reply(WrapError(exception.what()));
        }
      });
    } else {
      channel.SetMessageHandler(nullptr);
    }
  }
  {
    BasicMessageChannel<> channel(binary_messenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheCacheListener.onCacheStatusChanged", &GetCodec());
    if (api != nullptr) {
      channel.SetMessageHandler([api](const EncodableValue& message, const flutter::MessageReply<EncodableValue>& reply) {
        try {
          const auto& args = std::get<EncodableList>(message);
          const auto& encodable_url_arg = args.at(0);
          if (encodable_url_arg.IsNull()) {
            reply(WrapError("url_arg unexpectedly null."));
            return;
          }
          const auto& url_arg = std::get<std::string>(encodable_url_arg);
          const auto& encodable_status_arg = args.at(1);
          if (encodable_status_arg.IsNull()) {
            reply(WrapError("status_arg unexpectedly null."));
            return;
          }
          const CacheStatus& status_arg = (CacheStatus)encodable_status_arg.LongValue();
          std::optional<FlutterError> output = api->OnCacheStatusChanged(url_arg, status_arg);
          if (output.has_value()) {
            reply(WrapError(output.value()));
            return;
          }
          EncodableList wrapped;
          wrapped.push_back(EncodableValue());
          reply(EncodableValue(std::move(wrapped)));
        } catch (const std::exception& exception) {
          reply(WrapError(exception.what()));
        }
      });
    } else {
      channel.SetMessageHandler(nullptr);
    }
  }
  {
    BasicMessageChannel<> channel(binary_messenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheCacheListener.onCacheError", &GetCodec());
    if (api != nullptr) {
      channel.SetMessageHandler([api](const EncodableValue& message, const flutter::MessageReply<EncodableValue>& reply) {
        try {
          const auto& args = std::get<EncodableList>(message);
          const auto& encodable_url_arg = args.at(0);
          if (encodable_url_arg.IsNull()) {
            reply(WrapError("url_arg unexpectedly null."));
            return;
          }
          const auto& url_arg = std::get<std::string>(encodable_url_arg);
          const auto& encodable_error_arg = args.at(1);
          if (encodable_error_arg.IsNull()) {
            reply(WrapError("error_arg unexpectedly null."));
            return;
          }
          const auto& error_arg = std::get<std::string>(encodable_error_arg);
          std::optional<FlutterError> output = api->OnCacheError(url_arg, error_arg);
          if (output.has_value()) {
            reply(WrapError(output.value()));
            return;
          }
          EncodableList wrapped;
          wrapped.push_back(EncodableValue());
          reply(EncodableValue(std::move(wrapped)));
        } catch (const std::exception& exception) {
          reply(WrapError(exception.what()));
        }
      });
    } else {
      channel.SetMessageHandler(nullptr);
    }
  }
}

EncodableValue NativeVideoCacheCacheListener::WrapError(std::string_view error_message) {
  return EncodableValue(EncodableList{
    EncodableValue(std::string(error_message)),
    EncodableValue("Error"),
    EncodableValue()
  });
}

EncodableValue NativeVideoCacheCacheListener::WrapError(const FlutterError& error) {
  return EncodableValue(EncodableList{
    EncodableValue(error.code()),
    EncodableValue(error.message()),
    error.details()
  });
}


NativeVideoCacheApiCodecSerializer::NativeVideoCacheApiCodecSerializer() {}

EncodableValue NativeVideoCacheApiCodecSerializer::ReadValueOfType(
  uint8_t type,
  flutter::ByteStreamReader* stream) const {
  switch (type) {
    case 128:
      return CustomEncodableValue(CacheConfig::FromEncodableList(std::get<EncodableList>(ReadValue(stream))));
    case 129:
      return CustomEncodableValue(CacheInfo::FromEncodableList(std::get<EncodableList>(ReadValue(stream))));
    default:
      return flutter::StandardCodecSerializer::ReadValueOfType(type, stream);
  }
}

void NativeVideoCacheApiCodecSerializer::WriteValue(
  const EncodableValue& value,
  flutter::ByteStreamWriter* stream) const {
  if (const CustomEncodableValue* custom_value = std::get_if<CustomEncodableValue>(&value)) {
    if (custom_value->type() == typeid(CacheConfig)) {
      stream->WriteByte(128);
      WriteValue(EncodableValue(std::any_cast<CacheConfig>(*custom_value).ToEncodableList()), stream);
      return;
    }
    if (custom_value->type() == typeid(CacheInfo)) {
      stream->WriteByte(129);
      WriteValue(EncodableValue(std::any_cast<CacheInfo>(*custom_value).ToEncodableList()), stream);
      return;
    }
  }
  flutter::StandardCodecSerializer::WriteValue(value, stream);
}

/// The codec used by NativeVideoCacheApi.
const flutter::StandardMessageCodec& NativeVideoCacheApi::GetCodec() {
  return flutter::StandardMessageCodec::GetInstance(&NativeVideoCacheApiCodecSerializer::GetInstance());
}

// Sets up an instance of `NativeVideoCacheApi` to handle messages through the `binary_messenger`.
void NativeVideoCacheApi::SetUp(
  flutter::BinaryMessenger* binary_messenger,
  NativeVideoCacheApi* api) {
  {
    BasicMessageChannel<> channel(binary_messenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.initialize", &GetCodec());
    if (api != nullptr) {
      channel.SetMessageHandler([api](const EncodableValue& message, const flutter::MessageReply<EncodableValue>& reply) {
        try {
          const auto& args = std::get<EncodableList>(message);
          const auto& encodable_config_arg = args.at(0);
          if (encodable_config_arg.IsNull()) {
            reply(WrapError("config_arg unexpectedly null."));
            return;
          }
          const auto& config_arg = std::any_cast<const CacheConfig&>(std::get<CustomEncodableValue>(encodable_config_arg));
          api->Initialize(config_arg, [reply](std::optional<FlutterError>&& output) {
            if (output.has_value()) {
              reply(WrapError(output.value()));
              return;
            }
            EncodableList wrapped;
            wrapped.push_back(EncodableValue());
            reply(EncodableValue(std::move(wrapped)));
          });
        } catch (const std::exception& exception) {
          reply(WrapError(exception.what()));
        }
      });
    } else {
      channel.SetMessageHandler(nullptr);
    }
  }
  {
    BasicMessageChannel<> channel(binary_messenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.getProxyUrl", &GetCodec());
    if (api != nullptr) {
      channel.SetMessageHandler([api](const EncodableValue& message, const flutter::MessageReply<EncodableValue>& reply) {
        try {
          const auto& args = std::get<EncodableList>(message);
          const auto& encodable_original_url_arg = args.at(0);
          if (encodable_original_url_arg.IsNull()) {
            reply(WrapError("original_url_arg unexpectedly null."));
            return;
          }
          const auto& original_url_arg = std::get<std::string>(encodable_original_url_arg);
          api->GetProxyUrl(original_url_arg, [reply](ErrorOr<std::string>&& output) {
            if (output.has_error()) {
              reply(WrapError(output.error()));
              return;
            }
            EncodableList wrapped;
            wrapped.push_back(EncodableValue(std::move(output).TakeValue()));
            reply(EncodableValue(std::move(wrapped)));
          });
        } catch (const std::exception& exception) {
          reply(WrapError(exception.what()));
        }
      });
    } else {
      channel.SetMessageHandler(nullptr);
    }
  }
  {
    BasicMessageChannel<> channel(binary_messenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.startCache", &GetCodec());
    if (api != nullptr) {
      channel.SetMessageHandler([api](const EncodableValue& message, const flutter::MessageReply<EncodableValue>& reply) {
        try {
          const auto& args = std::get<EncodableList>(message);
          const auto& encodable_url_arg = args.at(0);
          if (encodable_url_arg.IsNull()) {
            reply(WrapError("url_arg unexpectedly null."));
            return;
          }
          const auto& url_arg = std::get<std::string>(encodable_url_arg);
          api->StartCache(url_arg, [reply](std::optional<FlutterError>&& output) {
            if (output.has_value()) {
              reply(WrapError(output.value()));
              return;
            }
            EncodableList wrapped;
            wrapped.push_back(EncodableValue());
            reply(EncodableValue(std::move(wrapped)));
          });
        } catch (const std::exception& exception) {
          reply(WrapError(exception.what()));
        }
      });
    } else {
      channel.SetMessageHandler(nullptr);
    }
  }
  {
    BasicMessageChannel<> channel(binary_messenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.stopCache", &GetCodec());
    if (api != nullptr) {
      channel.SetMessageHandler([api](const EncodableValue& message, const flutter::MessageReply<EncodableValue>& reply) {
        try {
          const auto& args = std::get<EncodableList>(message);
          const auto& encodable_url_arg = args.at(0);
          if (encodable_url_arg.IsNull()) {
            reply(WrapError("url_arg unexpectedly null."));
            return;
          }
          const auto& url_arg = std::get<std::string>(encodable_url_arg);
          api->StopCache(url_arg, [reply](std::optional<FlutterError>&& output) {
            if (output.has_value()) {
              reply(WrapError(output.value()));
              return;
            }
            EncodableList wrapped;
            wrapped.push_back(EncodableValue());
            reply(EncodableValue(std::move(wrapped)));
          });
        } catch (const std::exception& exception) {
          reply(WrapError(exception.what()));
        }
      });
    } else {
      channel.SetMessageHandler(nullptr);
    }
  }
  {
    BasicMessageChannel<> channel(binary_messenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.getCacheInfo", &GetCodec());
    if (api != nullptr) {
      channel.SetMessageHandler([api](const EncodableValue& message, const flutter::MessageReply<EncodableValue>& reply) {
        try {
          const auto& args = std::get<EncodableList>(message);
          const auto& encodable_url_arg = args.at(0);
          if (encodable_url_arg.IsNull()) {
            reply(WrapError("url_arg unexpectedly null."));
            return;
          }
          const auto& url_arg = std::get<std::string>(encodable_url_arg);
          api->GetCacheInfo(url_arg, [reply](ErrorOr<CacheInfo>&& output) {
            if (output.has_error()) {
              reply(WrapError(output.error()));
              return;
            }
            EncodableList wrapped;
            wrapped.push_back(CustomEncodableValue(std::move(output).TakeValue()));
            reply(EncodableValue(std::move(wrapped)));
          });
        } catch (const std::exception& exception) {
          reply(WrapError(exception.what()));
        }
      });
    } else {
      channel.SetMessageHandler(nullptr);
    }
  }
  {
    BasicMessageChannel<> channel(binary_messenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.clearAllCache", &GetCodec());
    if (api != nullptr) {
      channel.SetMessageHandler([api](const EncodableValue& message, const flutter::MessageReply<EncodableValue>& reply) {
        try {
          api->ClearAllCache([reply](std::optional<FlutterError>&& output) {
            if (output.has_value()) {
              reply(WrapError(output.value()));
              return;
            }
            EncodableList wrapped;
            wrapped.push_back(EncodableValue());
            reply(EncodableValue(std::move(wrapped)));
          });
        } catch (const std::exception& exception) {
          reply(WrapError(exception.what()));
        }
      });
    } else {
      channel.SetMessageHandler(nullptr);
    }
  }
  {
    BasicMessageChannel<> channel(binary_messenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.clearCache", &GetCodec());
    if (api != nullptr) {
      channel.SetMessageHandler([api](const EncodableValue& message, const flutter::MessageReply<EncodableValue>& reply) {
        try {
          const auto& args = std::get<EncodableList>(message);
          const auto& encodable_url_arg = args.at(0);
          if (encodable_url_arg.IsNull()) {
            reply(WrapError("url_arg unexpectedly null."));
            return;
          }
          const auto& url_arg = std::get<std::string>(encodable_url_arg);
          api->ClearCache(url_arg, [reply](std::optional<FlutterError>&& output) {
            if (output.has_value()) {
              reply(WrapError(output.value()));
              return;
            }
            EncodableList wrapped;
            wrapped.push_back(EncodableValue());
            reply(EncodableValue(std::move(wrapped)));
          });
        } catch (const std::exception& exception) {
          reply(WrapError(exception.what()));
        }
      });
    } else {
      channel.SetMessageHandler(nullptr);
    }
  }
  {
    BasicMessageChannel<> channel(binary_messenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.getTotalCacheSize", &GetCodec());
    if (api != nullptr) {
      channel.SetMessageHandler([api](const EncodableValue& message, const flutter::MessageReply<EncodableValue>& reply) {
        try {
          api->GetTotalCacheSize([reply](ErrorOr<int64_t>&& output) {
            if (output.has_error()) {
              reply(WrapError(output.error()));
              return;
            }
            EncodableList wrapped;
            wrapped.push_back(EncodableValue(std::move(output).TakeValue()));
            reply(EncodableValue(std::move(wrapped)));
          });
        } catch (const std::exception& exception) {
          reply(WrapError(exception.what()));
        }
      });
    } else {
      channel.SetMessageHandler(nullptr);
    }
  }
  {
    BasicMessageChannel<> channel(binary_messenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.isCached", &GetCodec());
    if (api != nullptr) {
      channel.SetMessageHandler([api](const EncodableValue& message, const flutter::MessageReply<EncodableValue>& reply) {
        try {
          const auto& args = std::get<EncodableList>(message);
          const auto& encodable_url_arg = args.at(0);
          if (encodable_url_arg.IsNull()) {
            reply(WrapError("url_arg unexpectedly null."));
            return;
          }
          const auto& url_arg = std::get<std::string>(encodable_url_arg);
          api->IsCached(url_arg, [reply](ErrorOr<bool>&& output) {
            if (output.has_error()) {
              reply(WrapError(output.error()));
              return;
            }
            EncodableList wrapped;
            wrapped.push_back(EncodableValue(std::move(output).TakeValue()));
            reply(EncodableValue(std::move(wrapped)));
          });
        } catch (const std::exception& exception) {
          reply(WrapError(exception.what()));
        }
      });
    } else {
      channel.SetMessageHandler(nullptr);
    }
  }
  {
    BasicMessageChannel<> channel(binary_messenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.setCacheListener", &GetCodec());
    if (api != nullptr) {
      channel.SetMessageHandler([api](const EncodableValue& message, const flutter::MessageReply<EncodableValue>& reply) {
        try {
          std::optional<FlutterError> output = api->SetCacheListener();
          if (output.has_value()) {
            reply(WrapError(output.value()));
            return;
          }
          EncodableList wrapped;
          wrapped.push_back(EncodableValue());
          reply(EncodableValue(std::move(wrapped)));
        } catch (const std::exception& exception) {
          reply(WrapError(exception.what()));
        }
      });
    } else {
      channel.SetMessageHandler(nullptr);
    }
  }
  {
    BasicMessageChannel<> channel(binary_messenger, "dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.removeCacheListener", &GetCodec());
    if (api != nullptr) {
      channel.SetMessageHandler([api](const EncodableValue& message, const flutter::MessageReply<EncodableValue>& reply) {
        try {
          std::optional<FlutterError> output = api->RemoveCacheListener();
          if (output.has_value()) {
            reply(WrapError(output.value()));
            return;
          }
          EncodableList wrapped;
          wrapped.push_back(EncodableValue());
          reply(EncodableValue(std::move(wrapped)));
        } catch (const std::exception& exception) {
          reply(WrapError(exception.what()));
        }
      });
    } else {
      channel.SetMessageHandler(nullptr);
    }
  }
}

EncodableValue NativeVideoCacheApi::WrapError(std::string_view error_message) {
  return EncodableValue(EncodableList{
    EncodableValue(std::string(error_message)),
    EncodableValue("Error"),
    EncodableValue()
  });
}

EncodableValue NativeVideoCacheApi::WrapError(const FlutterError& error) {
  return EncodableValue(EncodableList{
    EncodableValue(error.code()),
    EncodableValue(error.message()),
    error.details()
  });
}

}  // namespace native_video_cache
