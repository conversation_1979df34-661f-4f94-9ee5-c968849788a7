// Autogenerated from Pig<PERSON> (v17.3.0), do not edit directly.
// See also: https://pub.dev/packages/pigeon

#ifndef PIGEON_NATIVE_AV_API_H_
#define PIGEON_NATIVE_AV_API_H_
#include <flutter/basic_message_channel.h>
#include <flutter/binary_messenger.h>
#include <flutter/encodable_value.h>
#include <flutter/standard_message_codec.h>

#include <map>
#include <optional>
#include <string>

namespace native_av {


// Generated class from Pigeon.

class FlutterError {
 public:
  explicit FlutterError(const std::string& code)
    : code_(code) {}
  explicit FlutterError(const std::string& code, const std::string& message)
    : code_(code), message_(message) {}
  explicit FlutterError(const std::string& code, const std::string& message, const flutter::EncodableValue& details)
    : code_(code), message_(message), details_(details) {}

  const std::string& code() const { return code_; }
  const std::string& message() const { return message_; }
  const flutter::EncodableValue& details() const { return details_; }

 private:
  std::string code_;
  std::string message_;
  flutter::EncodableValue details_;
};

template<class T> class ErrorOr {
 public:
  ErrorOr(const T& rhs) : v_(rhs) {}
  ErrorOr(const T&& rhs) : v_(std::move(rhs)) {}
  ErrorOr(const FlutterError& rhs) : v_(rhs) {}
  ErrorOr(const FlutterError&& rhs) : v_(std::move(rhs)) {}

  bool has_error() const { return std::holds_alternative<FlutterError>(v_); }
  const T& value() const { return std::get<T>(v_); };
  const FlutterError& error() const { return std::get<FlutterError>(v_); };

 private:
  friend class NativeAvCacheListener;
  friend class NativeAvApi;
  ErrorOr() = default;
  T TakeValue() && { return std::get<T>(std::move(v_)); }

  std::variant<T, FlutterError> v_;
};


// 缓存状态枚举
enum class CacheStatus {
  // 未缓存
  none = 0,
  // 缓存中
  caching = 1,
  // 已缓存
  cached = 2,
  // 缓存错误
  error = 3
};

// 缓存配置类
//
// Generated class from Pigeon that represents data sent in messages.
class CacheConfig {
 public:
  // Constructs an object setting all non-nullable fields.
  CacheConfig();

  // Constructs an object setting all fields.
  explicit CacheConfig(
    const int64_t* max_cache_size,
    const std::string* cache_directory,
    const int64_t* max_concurrent_downloads,
    const int64_t* connect_timeout,
    const int64_t* read_timeout);

  // 最大缓存大小 (bytes)
  const int64_t* max_cache_size() const;
  void set_max_cache_size(const int64_t* value_arg);
  void set_max_cache_size(int64_t value_arg);

  // 缓存目录路径
  const std::string* cache_directory() const;
  void set_cache_directory(const std::string_view* value_arg);
  void set_cache_directory(std::string_view value_arg);

  // 最大并发下载数
  const int64_t* max_concurrent_downloads() const;
  void set_max_concurrent_downloads(const int64_t* value_arg);
  void set_max_concurrent_downloads(int64_t value_arg);

  // 连接超时时间 (milliseconds)
  const int64_t* connect_timeout() const;
  void set_connect_timeout(const int64_t* value_arg);
  void set_connect_timeout(int64_t value_arg);

  // 读取超时时间 (milliseconds)
  const int64_t* read_timeout() const;
  void set_read_timeout(const int64_t* value_arg);
  void set_read_timeout(int64_t value_arg);


 private:
  static CacheConfig FromEncodableList(const flutter::EncodableList& list);
  flutter::EncodableList ToEncodableList() const;
  friend class NativeAvCacheListener;
  friend class NativeAvCacheListenerCodecSerializer;
  friend class NativeAvApi;
  friend class NativeAvApiCodecSerializer;
  std::optional<int64_t> max_cache_size_;
  std::optional<std::string> cache_directory_;
  std::optional<int64_t> max_concurrent_downloads_;
  std::optional<int64_t> connect_timeout_;
  std::optional<int64_t> read_timeout_;

};


// 缓存信息类
//
// Generated class from Pigeon that represents data sent in messages.
class CacheInfo {
 public:
  // Constructs an object setting all non-nullable fields.
  explicit CacheInfo(
    const std::string& original_url,
    const CacheStatus& status,
    double progress,
    int64_t cached_size,
    int64_t total_size);

  // Constructs an object setting all fields.
  explicit CacheInfo(
    const std::string& original_url,
    const CacheStatus& status,
    double progress,
    int64_t cached_size,
    int64_t total_size,
    const std::string* error_message);

  // 原始URL
  const std::string& original_url() const;
  void set_original_url(std::string_view value_arg);

  // 缓存状态
  const CacheStatus& status() const;
  void set_status(const CacheStatus& value_arg);

  // 缓存进度 (0.0 - 1.0)
  double progress() const;
  void set_progress(double value_arg);

  // 已缓存大小 (bytes)
  int64_t cached_size() const;
  void set_cached_size(int64_t value_arg);

  // 总大小 (bytes)
  int64_t total_size() const;
  void set_total_size(int64_t value_arg);

  // 错误信息
  const std::string* error_message() const;
  void set_error_message(const std::string_view* value_arg);
  void set_error_message(std::string_view value_arg);


 private:
  static CacheInfo FromEncodableList(const flutter::EncodableList& list);
  flutter::EncodableList ToEncodableList() const;
  friend class NativeAvCacheListener;
  friend class NativeAvCacheListenerCodecSerializer;
  friend class NativeAvApi;
  friend class NativeAvApiCodecSerializer;
  std::string original_url_;
  CacheStatus status_;
  double progress_;
  int64_t cached_size_;
  int64_t total_size_;
  std::optional<std::string> error_message_;

};

// 缓存监听回调
//
// Generated interface from Pigeon that represents a handler of messages from Flutter.
class NativeAvCacheListener {
 public:
  NativeAvCacheListener(const NativeAvCacheListener&) = delete;
  NativeAvCacheListener& operator=(const NativeAvCacheListener&) = delete;
  virtual ~NativeAvCacheListener() {}
  // 缓存进度变化回调
  virtual std::optional<FlutterError> OnCacheProgressChanged(
    const std::string& url,
    double progress) = 0;
  // 缓存状态变化回调
  virtual std::optional<FlutterError> OnCacheStatusChanged(
    const std::string& url,
    const CacheStatus& status) = 0;
  // 缓存错误回调
  virtual std::optional<FlutterError> OnCacheError(
    const std::string& url,
    const std::string& error) = 0;

  // The codec used by NativeAvCacheListener.
  static const flutter::StandardMessageCodec& GetCodec();
  // Sets up an instance of `NativeAvCacheListener` to handle messages through the `binary_messenger`.
  static void SetUp(
    flutter::BinaryMessenger* binary_messenger,
    NativeAvCacheListener* api);
  static flutter::EncodableValue WrapError(std::string_view error_message);
  static flutter::EncodableValue WrapError(const FlutterError& error);

 protected:
  NativeAvCacheListener() = default;

};
class NativeAvApiCodecSerializer : public flutter::StandardCodecSerializer {
 public:
  NativeAvApiCodecSerializer();
  inline static NativeAvApiCodecSerializer& GetInstance() {
    static NativeAvApiCodecSerializer sInstance;
    return sInstance;
  }

  void WriteValue(
    const flutter::EncodableValue& value,
    flutter::ByteStreamWriter* stream) const override;

 protected:
  flutter::EncodableValue ReadValueOfType(
    uint8_t type,
    flutter::ByteStreamReader* stream) const override;

};

// Native Video Cache API
//
// Generated interface from Pigeon that represents a handler of messages from Flutter.
class NativeAvApi {
 public:
  NativeAvApi(const NativeAvApi&) = delete;
  NativeAvApi& operator=(const NativeAvApi&) = delete;
  virtual ~NativeAvApi() {}
  // 初始化缓存系统
  virtual void Initialize(
    const CacheConfig& config,
    std::function<void(std::optional<FlutterError> reply)> result) = 0;
  // 获取代理URL (用于播放器)
  virtual void GetProxyUrl(
    const std::string& original_url,
    std::function<void(ErrorOr<std::string> reply)> result) = 0;
  // 开始缓存视频
  virtual void StartCache(
    const std::string& url,
    std::function<void(std::optional<FlutterError> reply)> result) = 0;
  // 停止缓存视频
  virtual void StopCache(
    const std::string& url,
    std::function<void(std::optional<FlutterError> reply)> result) = 0;
  // 获取缓存信息
  virtual void GetCacheInfo(
    const std::string& url,
    std::function<void(ErrorOr<CacheInfo> reply)> result) = 0;
  // 清理所有缓存
  virtual void ClearAllCache(std::function<void(std::optional<FlutterError> reply)> result) = 0;
  // 清理指定URL的缓存
  virtual void ClearCache(
    const std::string& url,
    std::function<void(std::optional<FlutterError> reply)> result) = 0;
  // 获取总缓存大小
  virtual void GetTotalCacheSize(std::function<void(ErrorOr<int64_t> reply)> result) = 0;
  // 检查是否已缓存
  virtual void IsCached(
    const std::string& url,
    std::function<void(ErrorOr<bool> reply)> result) = 0;
  // 设置缓存监听器
  virtual std::optional<FlutterError> SetCacheListener() = 0;
  // 移除缓存监听器
  virtual std::optional<FlutterError> RemoveCacheListener() = 0;

  // The codec used by NativeAvApi.
  static const flutter::StandardMessageCodec& GetCodec();
  // Sets up an instance of `NativeAvApi` to handle messages through the `binary_messenger`.
  static void SetUp(
    flutter::BinaryMessenger* binary_messenger,
    NativeAvApi* api);
  static flutter::EncodableValue WrapError(std::string_view error_message);
  static flutter::EncodableValue WrapError(const FlutterError& error);

 protected:
  NativeAvApi() = default;

};
}  // namespace native_av
#endif  // PIGEON_NATIVE_AV_API_H_
