import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'native_video_cache_method_channel.dart';

abstract class NativeVideoCachePlatform extends PlatformInterface {
  /// Constructs a NativeVideoCachePlatform.
  NativeVideoCachePlatform() : super(token: _token);

  static final Object _token = Object();

  static NativeVideoCachePlatform _instance = MethodChannelNativeVideoCache();

  /// The default instance of [NativeVideoCachePlatform] to use.
  ///
  /// Defaults to [MethodChannelNativeVideoCache].
  static NativeVideoCachePlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [NativeVideoCachePlatform] when
  /// they register themselves.
  static set instance(NativeVideoCachePlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}
