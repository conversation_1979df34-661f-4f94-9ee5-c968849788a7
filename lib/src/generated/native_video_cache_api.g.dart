// Autogenerated from <PERSON><PERSON> (v17.3.0), do not edit directly.
// See also: https://pub.dev/packages/pigeon
// ignore_for_file: public_member_api_docs, non_constant_identifier_names, avoid_as, unused_import, unnecessary_parenthesis, prefer_null_aware_operators, omit_local_variable_types, unused_shown_name, unnecessary_import, no_leading_underscores_for_local_identifiers

import 'dart:async';
import 'dart:typed_data' show Float64List, Int32List, Int64List, Uint8List;

import 'package:flutter/foundation.dart' show ReadBuffer, WriteBuffer;
import 'package:flutter/services.dart';

PlatformException _createConnectionError(String channelName) {
  return PlatformException(
    code: 'channel-error',
    message: 'Unable to establish connection on channel: "$channelName".',
  );
}

/// 缓存状态枚举
enum CacheStatus {
  /// 未缓存
  none,
  /// 缓存中
  caching,
  /// 已缓存
  cached,
  /// 缓存错误
  error,
}

/// 缓存配置类
class CacheConfig {
  CacheConfig({
    this.maxCacheSize,
    this.cacheDirectory,
    this.maxConcurrentDownloads,
    this.connectTimeout,
    this.readTimeout,
  });

  /// 最大缓存大小 (bytes)
  int? maxCacheSize;

  /// 缓存目录路径
  String? cacheDirectory;

  /// 最大并发下载数
  int? maxConcurrentDownloads;

  /// 连接超时时间 (milliseconds)
  int? connectTimeout;

  /// 读取超时时间 (milliseconds)
  int? readTimeout;

  Object encode() {
    return <Object?>[
      maxCacheSize,
      cacheDirectory,
      maxConcurrentDownloads,
      connectTimeout,
      readTimeout,
    ];
  }

  static CacheConfig decode(Object result) {
    result as List<Object?>;
    return CacheConfig(
      maxCacheSize: result[0] as int?,
      cacheDirectory: result[1] as String?,
      maxConcurrentDownloads: result[2] as int?,
      connectTimeout: result[3] as int?,
      readTimeout: result[4] as int?,
    );
  }
}

/// 缓存信息类
class CacheInfo {
  CacheInfo({
    required this.originalUrl,
    required this.status,
    required this.progress,
    required this.cachedSize,
    required this.totalSize,
    this.errorMessage,
  });

  /// 原始URL
  String originalUrl;

  /// 缓存状态
  CacheStatus status;

  /// 缓存进度 (0.0 - 1.0)
  double progress;

  /// 已缓存大小 (bytes)
  int cachedSize;

  /// 总大小 (bytes)
  int totalSize;

  /// 错误信息
  String? errorMessage;

  Object encode() {
    return <Object?>[
      originalUrl,
      status.index,
      progress,
      cachedSize,
      totalSize,
      errorMessage,
    ];
  }

  static CacheInfo decode(Object result) {
    result as List<Object?>;
    return CacheInfo(
      originalUrl: result[0]! as String,
      status: CacheStatus.values[result[1]! as int],
      progress: result[2]! as double,
      cachedSize: result[3]! as int,
      totalSize: result[4]! as int,
      errorMessage: result[5] as String?,
    );
  }
}

/// 缓存监听回调
class NativeVideoCacheCacheListener {
  /// Constructor for [NativeVideoCacheCacheListener].  The [binaryMessenger] named argument is
  /// available for dependency injection.  If it is left null, the default
  /// BinaryMessenger will be used which routes to the host platform.
  NativeVideoCacheCacheListener({BinaryMessenger? binaryMessenger})
      : __pigeon_binaryMessenger = binaryMessenger;
  final BinaryMessenger? __pigeon_binaryMessenger;

  static const MessageCodec<Object?> pigeonChannelCodec = StandardMessageCodec();

  /// 缓存进度变化回调
  Future<void> onCacheProgressChanged(String url, double progress) async {
    const String __pigeon_channelName = 'dev.flutter.pigeon.native_video_cache.NativeVideoCacheCacheListener.onCacheProgressChanged';
    final BasicMessageChannel<Object?> __pigeon_channel = BasicMessageChannel<Object?>(
      __pigeon_channelName,
      pigeonChannelCodec,
      binaryMessenger: __pigeon_binaryMessenger,
    );
    final List<Object?>? __pigeon_replyList =
        await __pigeon_channel.send(<Object?>[url, progress]) as List<Object?>?;
    if (__pigeon_replyList == null) {
      throw _createConnectionError(__pigeon_channelName);
    } else if (__pigeon_replyList.length > 1) {
      throw PlatformException(
        code: __pigeon_replyList[0]! as String,
        message: __pigeon_replyList[1] as String?,
        details: __pigeon_replyList[2],
      );
    } else {
      return;
    }
  }

  /// 缓存状态变化回调
  Future<void> onCacheStatusChanged(String url, CacheStatus status) async {
    const String __pigeon_channelName = 'dev.flutter.pigeon.native_video_cache.NativeVideoCacheCacheListener.onCacheStatusChanged';
    final BasicMessageChannel<Object?> __pigeon_channel = BasicMessageChannel<Object?>(
      __pigeon_channelName,
      pigeonChannelCodec,
      binaryMessenger: __pigeon_binaryMessenger,
    );
    final List<Object?>? __pigeon_replyList =
        await __pigeon_channel.send(<Object?>[url, status.index]) as List<Object?>?;
    if (__pigeon_replyList == null) {
      throw _createConnectionError(__pigeon_channelName);
    } else if (__pigeon_replyList.length > 1) {
      throw PlatformException(
        code: __pigeon_replyList[0]! as String,
        message: __pigeon_replyList[1] as String?,
        details: __pigeon_replyList[2],
      );
    } else {
      return;
    }
  }

  /// 缓存错误回调
  Future<void> onCacheError(String url, String error) async {
    const String __pigeon_channelName = 'dev.flutter.pigeon.native_video_cache.NativeVideoCacheCacheListener.onCacheError';
    final BasicMessageChannel<Object?> __pigeon_channel = BasicMessageChannel<Object?>(
      __pigeon_channelName,
      pigeonChannelCodec,
      binaryMessenger: __pigeon_binaryMessenger,
    );
    final List<Object?>? __pigeon_replyList =
        await __pigeon_channel.send(<Object?>[url, error]) as List<Object?>?;
    if (__pigeon_replyList == null) {
      throw _createConnectionError(__pigeon_channelName);
    } else if (__pigeon_replyList.length > 1) {
      throw PlatformException(
        code: __pigeon_replyList[0]! as String,
        message: __pigeon_replyList[1] as String?,
        details: __pigeon_replyList[2],
      );
    } else {
      return;
    }
  }
}

class _NativeVideoCacheApiCodec extends StandardMessageCodec {
  const _NativeVideoCacheApiCodec();
  @override
  void writeValue(WriteBuffer buffer, Object? value) {
    if (value is CacheConfig) {
      buffer.putUint8(128);
      writeValue(buffer, value.encode());
    } else if (value is CacheInfo) {
      buffer.putUint8(129);
      writeValue(buffer, value.encode());
    } else {
      super.writeValue(buffer, value);
    }
  }

  @override
  Object? readValueOfType(int type, ReadBuffer buffer) {
    switch (type) {
      case 128: 
        return CacheConfig.decode(readValue(buffer)!);
      case 129: 
        return CacheInfo.decode(readValue(buffer)!);
      default:
        return super.readValueOfType(type, buffer);
    }
  }
}

/// Native Video Cache API
class NativeVideoCacheApi {
  /// Constructor for [NativeVideoCacheApi].  The [binaryMessenger] named argument is
  /// available for dependency injection.  If it is left null, the default
  /// BinaryMessenger will be used which routes to the host platform.
  NativeVideoCacheApi({BinaryMessenger? binaryMessenger})
      : __pigeon_binaryMessenger = binaryMessenger;
  final BinaryMessenger? __pigeon_binaryMessenger;

  static const MessageCodec<Object?> pigeonChannelCodec = _NativeVideoCacheApiCodec();

  /// 初始化缓存系统
  Future<void> initialize(CacheConfig config) async {
    const String __pigeon_channelName = 'dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.initialize';
    final BasicMessageChannel<Object?> __pigeon_channel = BasicMessageChannel<Object?>(
      __pigeon_channelName,
      pigeonChannelCodec,
      binaryMessenger: __pigeon_binaryMessenger,
    );
    final List<Object?>? __pigeon_replyList =
        await __pigeon_channel.send(<Object?>[config]) as List<Object?>?;
    if (__pigeon_replyList == null) {
      throw _createConnectionError(__pigeon_channelName);
    } else if (__pigeon_replyList.length > 1) {
      throw PlatformException(
        code: __pigeon_replyList[0]! as String,
        message: __pigeon_replyList[1] as String?,
        details: __pigeon_replyList[2],
      );
    } else {
      return;
    }
  }

  /// 获取代理URL (用于播放器)
  Future<String> getProxyUrl(String originalUrl) async {
    const String __pigeon_channelName = 'dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.getProxyUrl';
    final BasicMessageChannel<Object?> __pigeon_channel = BasicMessageChannel<Object?>(
      __pigeon_channelName,
      pigeonChannelCodec,
      binaryMessenger: __pigeon_binaryMessenger,
    );
    final List<Object?>? __pigeon_replyList =
        await __pigeon_channel.send(<Object?>[originalUrl]) as List<Object?>?;
    if (__pigeon_replyList == null) {
      throw _createConnectionError(__pigeon_channelName);
    } else if (__pigeon_replyList.length > 1) {
      throw PlatformException(
        code: __pigeon_replyList[0]! as String,
        message: __pigeon_replyList[1] as String?,
        details: __pigeon_replyList[2],
      );
    } else if (__pigeon_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (__pigeon_replyList[0] as String?)!;
    }
  }

  /// 开始缓存视频
  Future<void> startCache(String url) async {
    const String __pigeon_channelName = 'dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.startCache';
    final BasicMessageChannel<Object?> __pigeon_channel = BasicMessageChannel<Object?>(
      __pigeon_channelName,
      pigeonChannelCodec,
      binaryMessenger: __pigeon_binaryMessenger,
    );
    final List<Object?>? __pigeon_replyList =
        await __pigeon_channel.send(<Object?>[url]) as List<Object?>?;
    if (__pigeon_replyList == null) {
      throw _createConnectionError(__pigeon_channelName);
    } else if (__pigeon_replyList.length > 1) {
      throw PlatformException(
        code: __pigeon_replyList[0]! as String,
        message: __pigeon_replyList[1] as String?,
        details: __pigeon_replyList[2],
      );
    } else {
      return;
    }
  }

  /// 停止缓存视频
  Future<void> stopCache(String url) async {
    const String __pigeon_channelName = 'dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.stopCache';
    final BasicMessageChannel<Object?> __pigeon_channel = BasicMessageChannel<Object?>(
      __pigeon_channelName,
      pigeonChannelCodec,
      binaryMessenger: __pigeon_binaryMessenger,
    );
    final List<Object?>? __pigeon_replyList =
        await __pigeon_channel.send(<Object?>[url]) as List<Object?>?;
    if (__pigeon_replyList == null) {
      throw _createConnectionError(__pigeon_channelName);
    } else if (__pigeon_replyList.length > 1) {
      throw PlatformException(
        code: __pigeon_replyList[0]! as String,
        message: __pigeon_replyList[1] as String?,
        details: __pigeon_replyList[2],
      );
    } else {
      return;
    }
  }

  /// 获取缓存信息
  Future<CacheInfo> getCacheInfo(String url) async {
    const String __pigeon_channelName = 'dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.getCacheInfo';
    final BasicMessageChannel<Object?> __pigeon_channel = BasicMessageChannel<Object?>(
      __pigeon_channelName,
      pigeonChannelCodec,
      binaryMessenger: __pigeon_binaryMessenger,
    );
    final List<Object?>? __pigeon_replyList =
        await __pigeon_channel.send(<Object?>[url]) as List<Object?>?;
    if (__pigeon_replyList == null) {
      throw _createConnectionError(__pigeon_channelName);
    } else if (__pigeon_replyList.length > 1) {
      throw PlatformException(
        code: __pigeon_replyList[0]! as String,
        message: __pigeon_replyList[1] as String?,
        details: __pigeon_replyList[2],
      );
    } else if (__pigeon_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (__pigeon_replyList[0] as CacheInfo?)!;
    }
  }

  /// 清理所有缓存
  Future<void> clearAllCache() async {
    const String __pigeon_channelName = 'dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.clearAllCache';
    final BasicMessageChannel<Object?> __pigeon_channel = BasicMessageChannel<Object?>(
      __pigeon_channelName,
      pigeonChannelCodec,
      binaryMessenger: __pigeon_binaryMessenger,
    );
    final List<Object?>? __pigeon_replyList =
        await __pigeon_channel.send(null) as List<Object?>?;
    if (__pigeon_replyList == null) {
      throw _createConnectionError(__pigeon_channelName);
    } else if (__pigeon_replyList.length > 1) {
      throw PlatformException(
        code: __pigeon_replyList[0]! as String,
        message: __pigeon_replyList[1] as String?,
        details: __pigeon_replyList[2],
      );
    } else {
      return;
    }
  }

  /// 清理指定URL的缓存
  Future<void> clearCache(String url) async {
    const String __pigeon_channelName = 'dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.clearCache';
    final BasicMessageChannel<Object?> __pigeon_channel = BasicMessageChannel<Object?>(
      __pigeon_channelName,
      pigeonChannelCodec,
      binaryMessenger: __pigeon_binaryMessenger,
    );
    final List<Object?>? __pigeon_replyList =
        await __pigeon_channel.send(<Object?>[url]) as List<Object?>?;
    if (__pigeon_replyList == null) {
      throw _createConnectionError(__pigeon_channelName);
    } else if (__pigeon_replyList.length > 1) {
      throw PlatformException(
        code: __pigeon_replyList[0]! as String,
        message: __pigeon_replyList[1] as String?,
        details: __pigeon_replyList[2],
      );
    } else {
      return;
    }
  }

  /// 获取总缓存大小
  Future<int> getTotalCacheSize() async {
    const String __pigeon_channelName = 'dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.getTotalCacheSize';
    final BasicMessageChannel<Object?> __pigeon_channel = BasicMessageChannel<Object?>(
      __pigeon_channelName,
      pigeonChannelCodec,
      binaryMessenger: __pigeon_binaryMessenger,
    );
    final List<Object?>? __pigeon_replyList =
        await __pigeon_channel.send(null) as List<Object?>?;
    if (__pigeon_replyList == null) {
      throw _createConnectionError(__pigeon_channelName);
    } else if (__pigeon_replyList.length > 1) {
      throw PlatformException(
        code: __pigeon_replyList[0]! as String,
        message: __pigeon_replyList[1] as String?,
        details: __pigeon_replyList[2],
      );
    } else if (__pigeon_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (__pigeon_replyList[0] as int?)!;
    }
  }

  /// 检查是否已缓存
  Future<bool> isCached(String url) async {
    const String __pigeon_channelName = 'dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.isCached';
    final BasicMessageChannel<Object?> __pigeon_channel = BasicMessageChannel<Object?>(
      __pigeon_channelName,
      pigeonChannelCodec,
      binaryMessenger: __pigeon_binaryMessenger,
    );
    final List<Object?>? __pigeon_replyList =
        await __pigeon_channel.send(<Object?>[url]) as List<Object?>?;
    if (__pigeon_replyList == null) {
      throw _createConnectionError(__pigeon_channelName);
    } else if (__pigeon_replyList.length > 1) {
      throw PlatformException(
        code: __pigeon_replyList[0]! as String,
        message: __pigeon_replyList[1] as String?,
        details: __pigeon_replyList[2],
      );
    } else if (__pigeon_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (__pigeon_replyList[0] as bool?)!;
    }
  }

  /// 设置缓存监听器
  Future<void> setCacheListener() async {
    const String __pigeon_channelName = 'dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.setCacheListener';
    final BasicMessageChannel<Object?> __pigeon_channel = BasicMessageChannel<Object?>(
      __pigeon_channelName,
      pigeonChannelCodec,
      binaryMessenger: __pigeon_binaryMessenger,
    );
    final List<Object?>? __pigeon_replyList =
        await __pigeon_channel.send(null) as List<Object?>?;
    if (__pigeon_replyList == null) {
      throw _createConnectionError(__pigeon_channelName);
    } else if (__pigeon_replyList.length > 1) {
      throw PlatformException(
        code: __pigeon_replyList[0]! as String,
        message: __pigeon_replyList[1] as String?,
        details: __pigeon_replyList[2],
      );
    } else {
      return;
    }
  }

  /// 移除缓存监听器
  Future<void> removeCacheListener() async {
    const String __pigeon_channelName = 'dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.removeCacheListener';
    final BasicMessageChannel<Object?> __pigeon_channel = BasicMessageChannel<Object?>(
      __pigeon_channelName,
      pigeonChannelCodec,
      binaryMessenger: __pigeon_binaryMessenger,
    );
    final List<Object?>? __pigeon_replyList =
        await __pigeon_channel.send(null) as List<Object?>?;
    if (__pigeon_replyList == null) {
      throw _createConnectionError(__pigeon_channelName);
    } else if (__pigeon_replyList.length > 1) {
      throw PlatformException(
        code: __pigeon_replyList[0]! as String,
        message: __pigeon_replyList[1] as String?,
        details: __pigeon_replyList[2],
      );
    } else {
      return;
    }
  }
}
