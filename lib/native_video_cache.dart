library native_video_cache;

export 'src/native_video_cache_manager.dart';
export 'src/generated/native_video_cache_api.g.dart'
    show CacheConfig, CacheStatus, CacheInfo;

// 导出新的管理器和工具类
export 'src/cache_state_manager.dart';
export 'src/cache_error_handler.dart';
export 'src/cache_configuration.dart';
export 'src/cache_memory_manager.dart';
export 'src/cache_performance_monitor.dart';
export 'src/cache_stability_manager.dart';
export 'src/app_lifecycle_manager.dart';

/// Flutter视频缓存插件
/// 
/// 基于成熟的原生库实现高效视频缓存:
/// - iOS: KTVHTTPCache
/// - Android: AndroidVideoCache
/// 
/// 主要功能:
/// - 视频URL代理和缓存
/// - 缓存进度监听
/// - 缓存管理(清理、状态查询等)
/// 
/// 使用示例:
/// ```dart
/// // 初始化
/// await NativeVideoCacheManager.initialize(
///   CacheConfig(
///     maxCacheSize: 1024 * 1024 * 1024, // 1GB
///     maxConcurrentDownloads: 3,
///   ),
/// );
/// 
/// // 获取代理URL用于播放
/// final proxyUrl = await NativeVideoCacheManager.getProxyUrl('https://example.com/video.mp4');
/// 
/// // 播放器使用proxyUrl而不是原始URL
/// videoController.initialize(VideoPlayerController.network(proxyUrl));
/// ```
