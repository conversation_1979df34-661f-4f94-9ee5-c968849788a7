library native_video_cache;

export 'src/native_video_cache_manager.dart';
export 'src/generated/native_video_cache_api.g.dart'
    show CacheConfig, CacheStatus, CacheInfo;

/// Flutter视频缓存插件
/// 
/// 基于成熟的原生库实现高效视频缓存:
/// - iOS: KTVHTTPCache
/// - Android: AndroidVideoCache
/// 
/// 主要功能:
/// - 视频URL代理和缓存
/// - 缓存进度监听
/// - 缓存管理(清理、状态查询等)
/// 
/// 使用示例:
/// ```dart
/// // 初始化
/// await NativeVideoCacheManager.initialize(
///   CacheConfig(
///     maxCacheSize: 1024 * 1024 * 1024, // 1GB
///     maxConcurrentDownloads: 3,
///   ),
/// );
/// 
/// // 获取代理URL用于播放
/// final proxyUrl = await NativeVideoCacheManager.getProxyUrl('https://example.com/video.mp4');
/// 
/// // 播放器使用proxyUrl而不是原始URL
/// videoController.initialize(VideoPlayerController.network(proxyUrl));
/// ```
