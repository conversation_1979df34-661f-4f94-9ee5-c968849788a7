import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'native_video_cache_platform_interface.dart';

/// An implementation of [NativeVideoCachePlatform] that uses method channels.
class MethodChannelNativeVideoCache extends NativeVideoCachePlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('native_video_cache');

  @override
  Future<String?> getPlatformVersion() async {
    final version =
        await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }
}
