# 资源清理测试指南

## 问题描述
之前的问题：退出播放器界面后，后台仍在持续输出缓存相关日志，说明存在内存泄漏和监听器未正确清理的问题。

## 修复内容

### 1. 监听器清理
- ✅ 保存监听器引用（`_progressListener`, `_statusListener`, `_errorListener`）
- ✅ 在dispose时正确移除监听器
- ✅ 使用`NativeVideoCacheManager.removeProgressListener()`等方法

### 2. 缓存任务停止
- ✅ 在dispose时同步停止所有缓存任务
- ✅ 调用`NativeVideoCacheManager.stopCache(url)`停止每个URL的缓存
- ✅ 清理生命周期管理器中的活跃URL

### 3. 页面生命周期监听
- ✅ 实现`RouteAware`监听页面切换
- ✅ 在`didPop()`时执行强制清理
- ✅ 在`didPushNext()`时暂停缓存任务

### 4. 强制清理机制
- ✅ 多视频模式下调用`NativeVideoCacheManager.clearAllListeners()`
- ✅ 清理状态管理器中的所有状态
- ✅ 详细的调试日志输出

## 测试步骤

### 测试1：单视频播放模式
1. 启动应用
2. 选择"播放器功能演示"
3. 选择任意视频进入播放界面
4. 观察日志输出（应该有缓存相关日志）
5. **关键测试**：按返回键退出播放界面
6. **验证**：观察日志是否停止输出缓存相关信息

预期结果：
```
OptimizedCacheDemo: 开始清理资源...
OptimizedCacheDemo: 播放器已释放
OptimizedCacheDemo: 停止所有缓存任务...
OptimizedCacheDemo: 已停止当前视频缓存 - [URL]
OptimizedCacheDemo: 清理监听器...
OptimizedCacheDemo: 进度监听器已清理
OptimizedCacheDemo: 状态监听器已清理
OptimizedCacheDemo: 错误监听器已清理
OptimizedCacheDemo: 监听器清理完成
OptimizedCacheDemo: 资源清理完成
```

### 测试2：多视频管理模式
1. 启动应用
2. 选择"优化的缓存管理演示"
3. 点击"开始全部缓存"或单个缓存按钮
4. 观察缓存进度和日志输出
5. **关键测试**：按返回键退出管理界面
6. **验证**：观察日志是否停止输出缓存相关信息

预期结果：
```
OptimizedCacheDemo: 页面被弹出，执行清理...
OptimizedCacheDemo: 执行强制清理...
OptimizedCacheDemo: 停止所有缓存任务...
OptimizedCacheDemo: 已停止缓存 - [URL1]
OptimizedCacheDemo: 已停止缓存 - [URL2]
OptimizedCacheDemo: 清理监听器...
OptimizedCacheDemo: 进度监听器已清理
OptimizedCacheDemo: 状态监听器已清理
OptimizedCacheDemo: 错误监听器已清理
OptimizedCacheDemo: 监听器清理完成
OptimizedCacheDemo: 已清理所有缓存管理器监听器
OptimizedCacheDemo: 资源清理完成
```

### 测试3：内存泄漏验证
1. 重复进入和退出播放界面多次（5-10次）
2. 观察日志输出
3. **验证**：每次退出后，缓存相关日志应该完全停止
4. **验证**：不应该有持续的`BufferPoolAccessor`或`NativeVideoCachePlugin`日志

## 关键修复点

### 1. 监听器引用管理
```dart
// 保存引用
_progressListener = (String url, double progress) { ... };
NativeVideoCacheManager.addProgressListener(_progressListener!);

// 清理时移除
if (_progressListener != null) {
  NativeVideoCacheManager.removeProgressListener(_progressListener!);
  _progressListener = null;
}
```

### 2. 同步停止缓存任务
```dart
void _stopAllCacheTasksSync() {
  for (final url in _testUrls) {
    try {
      NativeVideoCacheManager.stopCache(url);
    } catch (e) {
      debugPrint('停止缓存失败: $e');
    }
  }
}
```

### 3. 页面生命周期处理
```dart
@override
void didPop() {
  _forceCleanup();
  super.didPop();
}
```

## 预期效果

修复后应该实现：
- ✅ 退出界面后立即停止所有缓存任务
- ✅ 正确清理所有监听器，避免内存泄漏
- ✅ 不再有持续的后台日志输出
- ✅ 页面切换时的资源管理更加可靠
- ✅ 支持多次进入/退出而不累积资源

如果测试通过，说明内存泄漏问题已经解决！
