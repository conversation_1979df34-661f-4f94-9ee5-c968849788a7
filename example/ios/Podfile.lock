PODS:
  - CocoaAsyncSocket (7.6.5)
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - Flutter (1.0.0)
  - integration_test (0.0.1):
    - Flutter
  - KTVHTTPCache (3.0.2):
    - CocoaAsyncSocket
  - native_video_cache (0.0.1):
    - Flutter
    - KTVHTTPCache (~> 3.0.0)
  - package_info_plus (0.4.5):
    - Flutter
  - ReachabilitySwift (5.2.4)
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - Flutter (from `Flutter`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - native_video_cache (from `.symlinks/plugins/native_video_cache/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)

SPEC REPOS:
  trunk:
    - CocoaAsyncSocket
    - KTVHTTPCache
    - ReachabilitySwift

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  Flutter:
    :path: Flutter
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  native_video_cache:
    :path: ".symlinks/plugins/native_video_cache/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"

SPEC CHECKSUMS:
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  connectivity_plus: bf0076dd84a130856aa636df1c71ccaff908fa1d
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  integration_test: 252f60fa39af5e17c3aa9899d35d908a0721b573
  KTVHTTPCache: 5711692cdf9a5ecfe829b1e16577deb3ffe3dc86
  native_video_cache: f8e18bcbc9c6be755f500919bc5ca957ca4ea308
  package_info_plus: 115f4ad11e0698c8c1c5d8a689390df880f47e85
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  wakelock_plus: 8b09852c8876491e4b6d179e17dfe2a0b5f60d47

PODFILE CHECKSUM: 7be2f5f74864d463a8ad433546ed1de7e0f29aef

COCOAPODS: 1.13.0
