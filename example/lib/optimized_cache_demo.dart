import 'package:flutter/material.dart';
import 'package:native_video_cache/native_video_cache.dart';

/// 优化后的缓存演示
class OptimizedCacheDemo extends StatefulWidget {
  const OptimizedCacheDemo({super.key});

  @override
  State<OptimizedCacheDemo> createState() => _OptimizedCacheDemoState();
}

class _OptimizedCacheDemoState extends State<OptimizedCacheDemo> {
  final List<String> _testUrls = [
    'https://new-jp.oss-cn-hangzhou.aliyuncs.com/jp_video/%E7%A7%91%E4%B8%802.2.2%E7%A6%81%E4%BB%A4%E6%A0%87%E5%BF%97.mp4',
    'https://new-jp.oss-cn-hangzhou.aliyuncs.com/jp_video/%E7%A7%91%E4%B8%802.1.1%E4%BA%A4%E9%80%9A%E4%BF%A1%E5%8F%B7%E7%81%AF%E7%9A%84%E5%88%86%E7%B1%BB%E3%80%81%E6%9C%BA%E5%8A%A8%E8%BD%A6%E5%92%8C%E9%9D%9E%E6%9C%BA%E5%8A%A8%E8%BD%A6%E4%BF%A1%E5%8F%B7%E7%81%AF.mp4',
  ];

  bool _isInitialized = false;
  String _statusMessage = '未初始化';

  @override
  void initState() {
    super.initState();
    _initializeOptimizedCache();
  }

  Future<void> _initializeOptimizedCache() async {
    try {
      setState(() {
        _statusMessage = '正在初始化优化的缓存系统...';
      });

      // 1. 配置系统
      final configuration = CacheConfiguration(
        global: const GlobalConfig(
          maxCacheSize: 2 * 1024 * 1024 * 1024, // 2GB
          enableDebugLogging: true,
          enablePerformanceMonitoring: true,
        ),
        performance: const PerformanceConfig(
          maxConcurrentDownloads: 5,
          maxActiveListeners: 100,
          maxCacheStates: 200,
        ),
        network: const NetworkConfig(
          connectTimeout: 30000,
          readTimeout: 60000,
          maxRetryAttempts: 3,
          enableBackgroundCaching: true,
        ),
      );

      // 2. 初始化配置管理器
      CacheConfigurationManager.instance.initialize(configuration);

      // 3. 初始化各个管理器
      CacheStateManager.instance.initialize();
      CacheMemoryManager.instance.initialize(
        maxUrls: configuration.performance.maxCacheStates,
        maxMemoryUsage: 10 * 1024 * 1024, // 10MB
      );
      await CacheStabilityManager.instance.initialize();
      CachePerformanceMonitor.instance.initialize();
      AppLifecycleManager.instance.initialize();

      // 4. 初始化主缓存管理器
      await NativeVideoCacheManager.initialize(
        CacheConfig(
          maxCacheSize: configuration.global.maxCacheSize,
          cacheDirectory: configuration.global.cacheDirectory,
          maxConcurrentDownloads: configuration.performance.maxConcurrentDownloads,
          connectTimeout: configuration.network.connectTimeout,
          readTimeout: configuration.network.readTimeout,
        ),
      );

      // 5. 设置状态监听
      _setupStateListeners();

      setState(() {
        _isInitialized = true;
        _statusMessage = '优化的缓存系统初始化完成';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '初始化失败: $e';
      });
    }
  }

  void _setupStateListeners() {
    // 监听统一状态变化
    CacheStateManager.instance.stateStream.listen((state) {
      debugPrint('状态变化: ${state.url} -> ${state.status} (${state.progress})');
    });

    // 为每个测试URL添加状态监听器
    for (final url in _testUrls) {
      CacheStateManager.instance.addListener(url, (state) {
        if (mounted) {
          setState(() {
            // 触发UI更新
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('优化的缓存演示'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 状态卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '系统状态',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(_statusMessage),
                    if (_isInitialized) ...[
                      const SizedBox(height: 16),
                      _buildSystemStats(),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 操作按钮
            if (_isInitialized) ...[
              Text(
                '缓存操作',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: [
                  ElevatedButton(
                    onPressed: _startAllCache,
                    child: const Text('开始全部缓存'),
                  ),
                  ElevatedButton(
                    onPressed: _stopAllCache,
                    child: const Text('停止全部缓存'),
                  ),
                  ElevatedButton(
                    onPressed: _clearAllCache,
                    child: const Text('清理全部缓存'),
                  ),
                  ElevatedButton(
                    onPressed: _showPerformanceReport,
                    child: const Text('性能报告'),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // URL列表
              Text(
                '缓存项目',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Expanded(
                child: ListView.builder(
                  itemCount: _testUrls.length,
                  itemBuilder: (context, index) {
                    final url = _testUrls[index];
                    return _buildUrlCard(url, index);
                  },
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSystemStats() {
    final stateStats = CacheStateManager.instance.getStatistics();
    final memoryStats = CacheMemoryManager.instance.getMemoryStats();
    final stabilityStats = CacheStabilityManager.instance.getStabilityStats();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('状态统计: ${stateStats['totalUrls']} 个URL'),
        Text('成功率: ${(stateStats['successRate'] * 100).toStringAsFixed(1)}%'),
        Text('内存监听器: ${memoryStats['totalListeners']}'),
        Text('重试任务: ${stabilityStats['totalRetryContexts']}'),
      ],
    );
  }

  Widget _buildUrlCard(String url, int index) {
    final state = CacheStateManager.instance.getState(url);
    final fileName = 'Video ${index + 1}';

    return Card(
      child: ListTile(
        title: Text(fileName),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              url,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(fontSize: 12),
            ),
            if (state != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  _buildStatusChip(state.status),
                  const SizedBox(width: 8),
                  Text('${(state.progress * 100).toStringAsFixed(1)}%'),
                ],
              ),
              if (state.progress > 0 && state.progress < 1)
                LinearProgressIndicator(value: state.progress),
            ],
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.play_arrow),
              onPressed: () => _startSingleCache(url),
            ),
            IconButton(
              icon: const Icon(Icons.stop),
              onPressed: () => _stopSingleCache(url),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(CacheStatus status) {
    Color color;
    String text;
    
    switch (status) {
      case CacheStatus.none:
        color = Colors.grey;
        text = '未缓存';
        break;
      case CacheStatus.caching:
        color = Colors.blue;
        text = '缓存中';
        break;
      case CacheStatus.cached:
        color = Colors.green;
        text = '已缓存';
        break;
      case CacheStatus.error:
        color = Colors.red;
        text = '错误';
        break;
    }

    return Chip(
      label: Text(text, style: const TextStyle(fontSize: 12)),
      backgroundColor: color.withOpacity(0.2),
      side: BorderSide(color: color),
    );
  }

  Future<void> _startAllCache() async {
    for (final url in _testUrls) {
      await _startSingleCache(url);
    }
  }

  Future<void> _stopAllCache() async {
    for (final url in _testUrls) {
      await _stopSingleCache(url);
    }
  }

  Future<void> _startSingleCache(String url) async {
    try {
      // 使用稳定性管理器的重试机制
      await CacheStabilityManager.instance.cacheWithRetry(url);
      
      // 更新状态
      CacheStateManager.instance.updateState(url, status: CacheStatus.caching);
      
      // 记录性能
      CachePerformanceMonitor.instance.recordCacheStart(url);
      
      // 注册到生命周期管理器
      AppLifecycleManager.instance.registerActiveUrl(url);
    } catch (e) {
      CacheStateManager.instance.updateState(
        url, 
        status: CacheStatus.error, 
        errorMessage: e.toString(),
      );
    }
  }

  Future<void> _stopSingleCache(String url) async {
    try {
      await NativeVideoCacheManager.stopCache(url);
      CacheStateManager.instance.updateState(url, status: CacheStatus.none);
      AppLifecycleManager.instance.unregisterActiveUrl(url);
    } catch (e) {
      debugPrint('停止缓存失败: $e');
    }
  }

  Future<void> _clearAllCache() async {
    try {
      await NativeVideoCacheManager.clearAllCache();
      CacheStateManager.instance.clearAllStates();
      setState(() {
        _statusMessage = '所有缓存已清理';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '清理缓存失败: $e';
      });
    }
  }

  void _showPerformanceReport() {
    final report = CachePerformanceMonitor.instance.getPerformanceReport();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('性能报告'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('总URL数: ${report['totalUrls']}'),
              Text('成功率: ${(report['overallSuccessRate'] * 100).toStringAsFixed(1)}%'),
              Text('平均耗时: ${report['averageDuration']?.toStringAsFixed(1) ?? 'N/A'}秒'),
              Text('总错误数: ${report['totalErrors']}'),
              Text('最近事件数: ${report['recentEventCount']}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    // 清理资源
    for (final url in _testUrls) {
      AppLifecycleManager.instance.unregisterActiveUrl(url);
    }
    super.dispose();
  }
}
