import 'package:native_video_cache/native_video_cache.dart';
import 'network_utils.dart';

/// 错误类型枚举
enum VideoErrorType {
  networkError, // 网络错误
  noCache, // 没有缓存
  urlInvalid, // URL无效
  formatUnsupported, // 格式不支持
  serverError, // 服务器错误
  unknown, // 未知错误
}

/// 视频播放错误处理器
class VideoErrorHandler {
  /// 分析错误并返回用户友好的错误信息
  static Future<VideoErrorInfo> analyzeError(
    String originalError,
    String videoUrl,
  ) async {
    // 1. 首先检查网络状态
    final isConnected = await NetworkUtils.isConnected();
    final networkDescription = await NetworkUtils.getNetworkStatusDescription();

    // 2. 检查是否已缓存
    bool isCached = false;
    try {
      isCached = await NativeVideoCacheManager.isCached(videoUrl);
    } catch (e) {
      // 忽略缓存检查错误
    }

    // 3. 分析原始错误信息
    final errorType = _categorizeError(originalError);

    // 4. 根据情况生成用户友好的错误信息
    return _generateFriendlyError(
      errorType: errorType,
      isConnected: isConnected,
      isCached: isCached,
      networkDescription: networkDescription,
      originalError: originalError,
      videoUrl: videoUrl,
    );
  }

  /// 分类错误类型
  static VideoErrorType _categorizeError(String error) {
    final errorLower = error.toLowerCase();

    // 网络相关错误的更精确匹配
    if (errorLower.contains('network') ||
        errorLower.contains('connection') ||
        errorLower.contains('timeout') ||
        errorLower.contains('unreachable') ||
        errorLower.contains('failed to resolve') ||
        errorLower.contains('socket') ||
        errorLower.contains('host') ||
        errorLower.contains('dns') ||
        errorLower.contains('connect timed out') ||
        errorLower.contains('no route to host')) {
      return VideoErrorType.networkError;
    }

    // URL无效的错误
    if ((errorLower.contains('invalid') && errorLower.contains('url')) ||
        errorLower.contains('malformed url') ||
        errorLower.contains('invalid uri')) {
      return VideoErrorType.urlInvalid;
    }

    // 格式不支持的错误
    if (errorLower.contains('unsupported') ||
        errorLower.contains('format') ||
        errorLower.contains('codec') ||
        errorLower.contains('not supported') ||
        errorLower.contains('unknown format')) {
      return VideoErrorType.formatUnsupported;
    }

    // 服务器错误
    if (errorLower.contains('404') ||
        errorLower.contains('403') ||
        errorLower.contains('500') ||
        errorLower.contains('502') ||
        errorLower.contains('503') ||
        errorLower.contains('server error') ||
        errorLower.contains('service unavailable') ||
        errorLower.contains('not found')) {
      return VideoErrorType.serverError;
    }

    // 初始化相关错误 - 通常是网络问题
    if (errorLower.contains('initialization') ||
        errorLower.contains('initialize') ||
        errorLower.contains('init failed') ||
        errorLower.contains('failed to load')) {
      return VideoErrorType.networkError;
    }

    return VideoErrorType.unknown;
  }

  /// 生成用户友好的错误信息
  static VideoErrorInfo _generateFriendlyError({
    required VideoErrorType errorType,
    required bool isConnected,
    required bool isCached,
    required String networkDescription,
    required String originalError,
    required String videoUrl,
  }) {
    String title;
    String message;
    List<String> suggestions = [];
    String icon;

    // 如果没有网络连接
    if (!isConnected) {
      if (isCached) {
        // 有缓存但没有网络 - 这种情况下理论上应该能播放缓存的视频
        title = '网络连接异常';
        message = '当前无网络连接，但视频已缓存，正在尝试播放离线内容';
        icon = '📡';
        suggestions.addAll([
          '视频已下载到本地，应该可以离线播放',
          '如果仍无法播放，请重新连接网络',
          '检查设备存储空间是否足够',
        ]);
      } else {
        // 没有缓存也没有网络 - 这是最常见的无法播放情况
        title = '网络连接异常';
        message = '当前无网络连接，需要连接网络才能播放在线视频';
        icon = '📡';
        suggestions.addAll([
          '请连接WiFi或移动网络',
          '确认网络连接正常后重试',
          '可以在有网络时预先缓存视频以便离线观看',
          '检查是否开启了飞行模式',
          '尝试重启网络连接',
        ]);
      }
    } else {
      // 有网络连接，根据错误类型分析
      switch (errorType) {
        case VideoErrorType.networkError:
          title = isCached ? '网络不稳定，尝试播放缓存' : '网络连接不稳定';
          message = isCached ? '网络连接不稳定，正在尝试播放已缓存的内容' : '无法稳定连接到视频服务器';
          icon = '🌐';
          if (isCached) {
            suggestions.addAll([
              '已检测到缓存内容，正在尝试离线播放',
              '如果缓存内容无法播放，请检查网络连接',
              '等待网络稳定后重试在线播放',
            ]);
          } else {
            suggestions.addAll([
              '检查网络信号强度',
              '尝试切换网络连接（WiFi/移动网络）',
              '稍后重试',
              '可以先缓存视频以便离线观看',
            ]);
          }
          break;

        case VideoErrorType.urlInvalid:
          title = '视频地址无效';
          message = '视频链接可能已失效或不正确';
          icon = '🔗';
          suggestions.addAll([
            '确认视频链接是否正确',
            '联系内容提供方确认视频状态',
            '尝试刷新页面或重新获取链接',
          ]);
          break;

        case VideoErrorType.formatUnsupported:
          title = '视频格式不支持';
          message = '当前设备不支持此视频格式';
          icon = '🎬';
          suggestions.addAll([
            '尝试使用其他播放器',
            '联系技术支持获取兼容格式',
            '查看设备是否支持该视频编码',
          ]);
          break;

        case VideoErrorType.serverError:
          title = '服务器暂时不可用';
          message = '视频服务器出现问题';
          icon = '🔧';
          suggestions.addAll([
            '服务器可能正在维护',
            '请稍后重试',
            '如问题持续，请联系客服',
            isCached ? '可以尝试播放已缓存的内容' : '建议稍后缓存视频以备离线观看',
          ]);
          break;

        case VideoErrorType.noCache:
          title = '视频未缓存';
          message = '需要网络连接来播放视频';
          icon = '💾';
          suggestions.addAll([
            '请确保网络连接正常',
            '正在尝试在线播放...',
            '可以预先缓存视频以离线观看',
          ]);
          break;

        case VideoErrorType.unknown:
        default:
          title = '播放出现问题';
          message = isCached ? '视频暂时无法播放，正在尝试播放缓存内容' : '视频暂时无法播放';
          icon = '⚠️';
          if (isCached) {
            suggestions.addAll([
              '正在尝试播放已缓存的内容',
              '如果缓存播放失败，请检查网络连接',
              '尝试重新播放',
            ]);
          } else {
            suggestions.addAll([
              '请检查网络连接',
              '尝试重新播放',
              '如问题持续，请联系技术支持',
              '建议缓存视频以便离线观看',
            ]);
          }
      }
    }

    return VideoErrorInfo(
      title: title,
      message: message,
      suggestions: suggestions,
      icon: icon,
      originalError: originalError,
      isNetworkError: !isConnected || errorType == VideoErrorType.networkError,
      canRetry: true, // 总是允许重试
    );
  }
}

/// 视频错误信息类
class VideoErrorInfo {
  final String title; // 错误标题
  final String message; // 错误描述
  final List<String> suggestions; // 解决建议
  final String icon; // 显示图标
  final String originalError; // 原始错误信息
  final bool isNetworkError; // 是否为网络错误
  final bool canRetry; // 是否可以重试

  const VideoErrorInfo({
    required this.title,
    required this.message,
    required this.suggestions,
    required this.icon,
    required this.originalError,
    required this.isNetworkError,
    required this.canRetry,
  });
}
