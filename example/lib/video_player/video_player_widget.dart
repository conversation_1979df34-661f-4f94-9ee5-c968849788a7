import 'package:flutter/material.dart';
import 'package:chewie/chewie.dart';
import 'video_player_controller.dart';

/// 自定义UI控制层的构建器
typedef ControlsBuilder = Widget Function(
  BuildContext context,
  NativeAvPlayerController controller,
);

/// 视频播放器Widget
class NativeAvPlayerWidget extends StatefulWidget {
  /// 播放器控制器
  final NativeAvPlayerController controller;

  /// 自定义控制层构建器
  final ControlsBuilder? controlsBuilder;

  /// 是否显示默认控制层
  final bool showDefaultControls;

  /// 播放器背景色
  final Color backgroundColor;

  /// 加载指示器
  final Widget? loadingIndicator;

  /// 错误页面构建器
  final Widget Function(BuildContext context, String error)? errorBuilder;

  const NativeAvPlayerWidget({
    super.key,
    required this.controller,
    this.controlsBuilder,
    this.showDefaultControls = true,
    this.backgroundColor = Colors.black,
    this.loadingIndicator,
    this.errorBuilder,
  });

  @override
  State<NativeAvPlayerWidget> createState() => _NativeAvPlayerWidgetState();
}

class _NativeAvPlayerWidgetState extends State<NativeAvPlayerWidget> {
  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onControllerUpdate);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onControllerUpdate);
    super.dispose();
  }

  void _onControllerUpdate() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: widget.backgroundColor,
      child: _buildPlayerContent(),
    );
  }

  Widget _buildPlayerContent() {
    final state = widget.controller.state;

    switch (state) {
      case NativeAvPlayerState.idle:
        return _buildIdleWidget();
      case NativeAvPlayerState.loading:
        return _buildLoadingWidget();
      case NativeAvPlayerState.error:
        return _buildErrorWidget();
      case NativeAvPlayerState.ready:
      case NativeAvPlayerState.playing:
      case NativeAvPlayerState.paused:
      case NativeAvPlayerState.buffering:
        return _buildVideoWidget();
      case NativeAvPlayerState.disposed:
        return _buildDisposedWidget();
    }
  }

  Widget _buildIdleWidget() {
    return const Center(
      child: Icon(
        Icons.play_circle_outline,
        size: 64,
        color: Colors.white54,
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: widget.loadingIndicator ??
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
    );
  }

  Widget _buildErrorWidget() {
    final error = widget.controller.errorMessage ?? '未知错误';

    if (widget.errorBuilder != null) {
      return widget.errorBuilder!(context, error);
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          const Text(
            '播放失败',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => widget.controller.reload(),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoWidget() {
    if (!widget.controller.isInitialized ||
        widget.controller.chewieController == null) {
      return _buildLoadingWidget();
    }

    return Stack(
      children: [
        // 视频播放器（包含chewie内置控制层）
        Positioned.fill(
          child: AspectRatio(
            aspectRatio: widget.controller.aspectRatio,
            child: Chewie(controller: widget.controller.chewieController!),
          ),
        ),

        // 只有在提供了自定义控制层构建器时才显示
        if (widget.controlsBuilder != null)
          widget.controlsBuilder!(context, widget.controller),
      ],
    );
  }

  Widget _buildDisposedWidget() {
    return const Center(
      child: Text(
        '播放器已释放',
        style: TextStyle(
          color: Colors.white54,
          fontSize: 16,
        ),
      ),
    );
  }
}
