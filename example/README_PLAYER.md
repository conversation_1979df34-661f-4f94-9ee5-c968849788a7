# Native AV 视频播放器

基于 `chewie` 和 `video_player` 的自定义视频播放器，与 KTVHTTPCache 缓存系统完美集成。

## 功能特性

### 🎯 核心功能
- **播放控制**: 播放、暂停、停止、跳转
- **速率控制**: 支持 0.5x, 0.75x, 1.0x, 1.25x, 1.5x, 2.0x 播放速度
- **进度控制**: 支持拖拽进度条快速跳转
- **全屏支持**: 横屏全屏播放体验
- **音量控制**: 音量调节功能
- **缓存集成**: 与 KTVHTTPCache 无缝集成，支持边播边缓存

### 🎨 UI控制层
- **独立封装**: UI控制层与播放器逻辑完全分离
- **自定义支持**: 外部可完全自定义控制层UI
- **默认控制层**: 提供美观实用的默认控制界面
- **状态管理**: 完善的播放状态管理和UI反馈

## 架构设计

```
NativeAvPlayerWidget (播放器容器)
├── NativeAvPlayerController (播放器控制器)
│   ├── VideoPlayerController (底层播放器)
│   ├── ChewieController (Chewie控制器)
│   └── 状态管理 & 事件回调
├── 自定义控制层 (可选)
└── DefaultVideoControls (默认控制层)
```

## 快速开始

### 1. 基本使用

```dart
import 'package:native_av_example/video_player/video_player_controller.dart';
import 'package:native_av_example/video_player/video_player_widget.dart';

class MyVideoPage extends StatefulWidget {
  @override
  _MyVideoPageState createState() => _MyVideoPageState();
}

class _MyVideoPageState extends State<MyVideoPage> {
  late NativeAvPlayerController _controller;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  Future<void> _initializePlayer() async {
    _controller = NativeAvPlayerController();
    
    // 设置事件监听器
    _controller.onStateChanged = (state) {
      print('播放状态: $state');
    };
    
    _controller.onError = (error) {
      print('播放错误: $error');
    };
    
    // 初始化播放器
    await _controller.initialize(
      'https://your-video-url.mp4',
      autoPlay: true,
      allowFullScreen: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AspectRatio(
        aspectRatio: 16 / 9,
        child: NativeAvPlayerWidget(
          controller: _controller,
          showDefaultControls: true,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
```

### 2. 自定义控制层

```dart
// 使用自定义控制层
NativeAvPlayerWidget(
  controller: _controller,
  showDefaultControls: false,
  controlsBuilder: (context, controller) {
    return MyCustomControls(controller: controller);
  },
)

// 自定义控制层示例
class MyCustomControls extends StatelessWidget {
  final NativeAvPlayerController controller;
  
  const MyCustomControls({Key? key, required this.controller}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 你的自定义UI
        Center(
          child: IconButton(
            onPressed: () {
              if (controller.isPlaying) {
                controller.pause();
              } else {
                controller.play();
              }
            },
            icon: Icon(
              controller.isPlaying ? Icons.pause : Icons.play_arrow,
              size: 64,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }
}
```

## API 参考

### NativeAvPlayerController

#### 属性
- `isInitialized`: 是否已初始化
- `state`: 当前播放状态
- `isPlaying`: 是否正在播放
- `isBuffering`: 是否正在缓冲
- `position`: 当前播放位置
- `duration`: 视频总时长
- `aspectRatio`: 视频宽高比

#### 方法
- `initialize(url, {...})`: 初始化播放器
- `play()`: 播放
- `pause()`: 暂停
- `stop()`: 停止
- `seekTo(position)`: 跳转到指定位置
- `setPlaybackSpeed(speed)`: 设置播放速度
- `setVolume(volume)`: 设置音量
- `toggleFullScreen()`: 切换全屏状态
- `dispose()`: 释放资源

#### 事件回调
- `onStateChanged`: 播放状态变化
- `onProgressChanged`: 播放进度变化
- `onError`: 播放错误

### NativeAvPlayerWidget

#### 参数
- `controller`: 播放器控制器（必需）
- `controlsBuilder`: 自定义控制层构建器
- `showDefaultControls`: 是否显示默认控制层
- `backgroundColor`: 播放器背景色
- `loadingIndicator`: 自定义加载指示器
- `errorBuilder`: 自定义错误页面构建器

## 播放状态

```dart
enum NativeAvPlayerState {
  idle,        // 空闲
  loading,     // 加载中
  ready,       // 准备就绪
  playing,     // 播放中
  paused,      // 已暂停
  buffering,   // 缓冲中
  error,       // 错误
  disposed,    // 已释放
}
```

## 与缓存系统集成

播放器自动与 KTVHTTPCache 缓存系统集成：

1. **自动获取代理URL**: 播放器会自动调用 `NativeAvManager.getProxyUrl()` 获取代理URL
2. **边播边缓存**: 播放过程中自动进行缓存
3. **缓存状态同步**: 播放器状态与缓存状态保持同步

## 最佳实践

### 1. 错误处理
```dart
_controller.onError = (error) {
  // 记录错误日志
  print('播放错误: $error');
  
  // 显示用户友好的错误信息
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(content: Text('播放失败，请重试')),
  );
  
  // 可以尝试重新加载
  _controller.reload();
};
```

### 2. 生命周期管理
```dart
@override
void dispose() {
  // 确保释放播放器资源
  _controller.dispose();
  super.dispose();
}
```

### 3. 状态监听
```dart
_controller.onStateChanged = (state) {
  switch (state) {
    case NativeAvPlayerState.loading:
      // 显示加载指示器
      break;
    case NativeAvPlayerState.ready:
      // 播放器准备就绪
      break;
    case NativeAvPlayerState.error:
      // 处理错误状态
      break;
  }
};
```

## 常见问题

### Q: 如何实现自动播放下一个视频？
A: 监听播放完成事件，然后调用下一个视频的初始化方法：

```dart
_controller.onProgressChanged = (position, duration) {
  if (position >= duration) {
    // 播放完成，播放下一个视频
    _playNextVideo();
  }
};
```

### Q: 如何保存播放进度？
A: 监听进度变化并定期保存：

```dart
_controller.onProgressChanged = (position, duration) {
  // 每10秒保存一次进度
  if (position.inSeconds % 10 == 0) {
    _savePlaybackProgress(position);
  }
};
```

### Q: 如何实现画中画功能？
A: 可以使用 `flutter_pip` 插件配合我们的播放器：

```dart
// 需要额外安装 flutter_pip 插件
// 在适当的时机调用
PictureInPicture.enterPictureInPictureMode();
```

## 注意事项

1. **资源管理**: 确保在不需要时调用 `dispose()` 释放资源
2. **网络权限**: 确保应用有网络访问权限
3. **iOS配置**: 确保在 iOS 中正确配置了 AVAudioSession
4. **Android配置**: 确保在 Android 中添加了必要的权限

## 更新日志

### v1.0.0
- 初始版本发布
- 基础播放控制功能
- 自定义控制层支持
- 与缓存系统集成 