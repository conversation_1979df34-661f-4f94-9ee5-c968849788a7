#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
# Run `pod lib lint native_video_cache.podspec` to validate before publishing.
#
Pod::Spec.new do |s|
  s.name             = 'native_video_cache'
  s.version          = '0.0.1'
  s.summary          = 'Flutter视频缓存插件，基于成熟的原生库实现高效视频缓存'
  s.description      = <<-DESC
Flutter视频缓存插件，基于成熟的原生库(iOS:KTVHTTPCache, Android:AndroidVideoCache)实现高效视频缓存
                       DESC
  s.homepage         = 'https://github.com/your-company/native_video_cache'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'Your Company' => '<EMAIL>' }
  s.source           = { :path => '.' }
  s.source_files = 'Classes/**/*'
  s.dependency 'Flutter'
  s.dependency 'KTVHTTPCache', '~> 3.0.0'
  s.platform = :ios, '11.0'

  # Flutter.framework does not contain a i386 slice.
  s.pod_target_xcconfig = { 'DEFINES_MODULE' => 'YES', 'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'i386' }
end
