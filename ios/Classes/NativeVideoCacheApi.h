// Autogenerated from <PERSON><PERSON> (v17.3.0), do not edit directly.
// See also: https://pub.dev/packages/pigeon

#import <Foundation/Foundation.h>

@protocol FlutterBinaryMessenger;
@protocol FlutterMessageCodec;
@class FlutterError;
@class FlutterStandardTypedData;

NS_ASSUME_NONNULL_BEGIN

/// 缓存状态枚举
typedef NS_ENUM(NSUInteger, NVCCacheStatus) {
  /// 未缓存
  NVCCacheStatusNone = 0,
  /// 缓存中
  NVCCacheStatusCaching = 1,
  /// 已缓存
  NVCCacheStatusCached = 2,
  /// 缓存错误
  NVCCacheStatusError = 3,
};

/// Wrapper for NVCCacheStatus to allow for nullability.
@interface NVCCacheStatusBox : NSObject
@property(nonatomic, assign) NVCCacheStatus value;
- (instancetype)initWithValue:(NVCCacheStatus)value;
@end

@class NVCCacheConfig;
@class NVCCacheInfo;

/// 缓存配置类
@interface NVCCacheConfig : NSObject
+ (instancetype)makeWithMaxCacheSize:(nullable NSNumber *)maxCacheSize
    cacheDirectory:(nullable NSString *)cacheDirectory
    maxConcurrentDownloads:(nullable NSNumber *)maxConcurrentDownloads
    connectTimeout:(nullable NSNumber *)connectTimeout
    readTimeout:(nullable NSNumber *)readTimeout;
/// 最大缓存大小 (bytes)
@property(nonatomic, strong, nullable) NSNumber * maxCacheSize;
/// 缓存目录路径
@property(nonatomic, copy, nullable) NSString * cacheDirectory;
/// 最大并发下载数
@property(nonatomic, strong, nullable) NSNumber * maxConcurrentDownloads;
/// 连接超时时间 (milliseconds)
@property(nonatomic, strong, nullable) NSNumber * connectTimeout;
/// 读取超时时间 (milliseconds)
@property(nonatomic, strong, nullable) NSNumber * readTimeout;
@end

/// 缓存信息类
@interface NVCCacheInfo : NSObject
/// `init` unavailable to enforce nonnull fields, see the `make` class method.
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)makeWithOriginalUrl:(NSString *)originalUrl
    status:(NVCCacheStatus)status
    progress:(double )progress
    cachedSize:(NSInteger )cachedSize
    totalSize:(NSInteger )totalSize
    errorMessage:(nullable NSString *)errorMessage;
/// 原始URL
@property(nonatomic, copy) NSString * originalUrl;
/// 缓存状态
@property(nonatomic, assign) NVCCacheStatus status;
/// 缓存进度 (0.0 - 1.0)
@property(nonatomic, assign) double  progress;
/// 已缓存大小 (bytes)
@property(nonatomic, assign) NSInteger  cachedSize;
/// 总大小 (bytes)
@property(nonatomic, assign) NSInteger  totalSize;
/// 错误信息
@property(nonatomic, copy, nullable) NSString * errorMessage;
@end

/// The codec used by NVCNativeVideoCacheCacheListener.
NSObject<FlutterMessageCodec> *NVCNativeVideoCacheCacheListenerGetCodec(void);

/// 缓存监听回调
@protocol NVCNativeVideoCacheCacheListener
/// 缓存进度变化回调
- (void)onCacheProgressChangedUrl:(NSString *)url progress:(double)progress error:(FlutterError *_Nullable *_Nonnull)error;
/// 缓存状态变化回调
- (void)onCacheStatusChangedUrl:(NSString *)url status:(NVCCacheStatus)status error:(FlutterError *_Nullable *_Nonnull)error;
/// 缓存错误回调
- (void)onCacheErrorUrl:(NSString *)url error:(NSString *)error error:(FlutterError *_Nullable *_Nonnull)error;
@end

extern void SetUpNVCNativeVideoCacheCacheListener(id<FlutterBinaryMessenger> binaryMessenger, NSObject<NVCNativeVideoCacheCacheListener> *_Nullable api);

/// The codec used by NVCNativeVideoCacheApi.
NSObject<FlutterMessageCodec> *NVCNativeVideoCacheApiGetCodec(void);

/// Native Video Cache API
@protocol NVCNativeVideoCacheApi
/// 初始化缓存系统
- (void)initializeConfig:(NVCCacheConfig *)config completion:(void (^)(FlutterError *_Nullable))completion;
/// 获取代理URL (用于播放器)
- (void)getProxyUrlOriginalUrl:(NSString *)originalUrl completion:(void (^)(NSString *_Nullable, FlutterError *_Nullable))completion;
/// 开始缓存视频
- (void)startCacheUrl:(NSString *)url completion:(void (^)(FlutterError *_Nullable))completion;
/// 停止缓存视频
- (void)stopCacheUrl:(NSString *)url completion:(void (^)(FlutterError *_Nullable))completion;
/// 获取缓存信息
- (void)getCacheInfoUrl:(NSString *)url completion:(void (^)(NVCCacheInfo *_Nullable, FlutterError *_Nullable))completion;
/// 清理所有缓存
- (void)clearAllCacheWithCompletion:(void (^)(FlutterError *_Nullable))completion;
/// 清理指定URL的缓存
- (void)clearCacheUrl:(NSString *)url completion:(void (^)(FlutterError *_Nullable))completion;
/// 获取总缓存大小
- (void)getTotalCacheSizeWithCompletion:(void (^)(NSNumber *_Nullable, FlutterError *_Nullable))completion;
/// 检查是否已缓存
- (void)isCachedUrl:(NSString *)url completion:(void (^)(NSNumber *_Nullable, FlutterError *_Nullable))completion;
/// 设置缓存监听器
- (void)setCacheListenerWithError:(FlutterError *_Nullable *_Nonnull)error;
/// 移除缓存监听器
- (void)removeCacheListenerWithError:(FlutterError *_Nullable *_Nonnull)error;
@end

extern void SetUpNVCNativeVideoCacheApi(id<FlutterBinaryMessenger> binaryMessenger, NSObject<NVCNativeVideoCacheApi> *_Nullable api);

NS_ASSUME_NONNULL_END
