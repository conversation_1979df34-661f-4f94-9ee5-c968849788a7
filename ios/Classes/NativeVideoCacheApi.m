// Autogenerated from <PERSON>eon (v17.3.0), do not edit directly.
// See also: https://pub.dev/packages/pigeon

#import "NativeVideoCacheApi.h"

#if TARGET_OS_OSX
#import <FlutterMacOS/FlutterMacOS.h>
#else
#import <Flutter/Flutter.h>
#endif

#if !__has_feature(objc_arc)
#error File requires ARC to be enabled.
#endif

static NSArray *wrapResult(id result, FlutterError *error) {
  if (error) {
    return @[
      error.code ?: [NSNull null], error.message ?: [NSNull null], error.details ?: [NSNull null]
    ];
  }
  return @[ result ?: [NSNull null] ];
}

static id GetNullableObjectAtIndex(NSArray *array, NSInteger key) {
  id result = array[key];
  return (result == [NSNull null]) ? nil : result;
}

/// 缓存状态枚举
@implementation NVCCacheStatusBox
- (instancetype)initWithValue:(NVCCacheStatus)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

@interface NVCCacheConfig ()
+ (NVCCacheConfig *)fromList:(NSArray *)list;
+ (nullable NVCCacheConfig *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface NVCCacheInfo ()
+ (NVCCacheInfo *)fromList:(NSArray *)list;
+ (nullable NVCCacheInfo *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@implementation NVCCacheConfig
+ (instancetype)makeWithMaxCacheSize:(nullable NSNumber *)maxCacheSize
    cacheDirectory:(nullable NSString *)cacheDirectory
    maxConcurrentDownloads:(nullable NSNumber *)maxConcurrentDownloads
    connectTimeout:(nullable NSNumber *)connectTimeout
    readTimeout:(nullable NSNumber *)readTimeout {
  NVCCacheConfig* pigeonResult = [[NVCCacheConfig alloc] init];
  pigeonResult.maxCacheSize = maxCacheSize;
  pigeonResult.cacheDirectory = cacheDirectory;
  pigeonResult.maxConcurrentDownloads = maxConcurrentDownloads;
  pigeonResult.connectTimeout = connectTimeout;
  pigeonResult.readTimeout = readTimeout;
  return pigeonResult;
}
+ (NVCCacheConfig *)fromList:(NSArray *)list {
  NVCCacheConfig *pigeonResult = [[NVCCacheConfig alloc] init];
  pigeonResult.maxCacheSize = GetNullableObjectAtIndex(list, 0);
  pigeonResult.cacheDirectory = GetNullableObjectAtIndex(list, 1);
  pigeonResult.maxConcurrentDownloads = GetNullableObjectAtIndex(list, 2);
  pigeonResult.connectTimeout = GetNullableObjectAtIndex(list, 3);
  pigeonResult.readTimeout = GetNullableObjectAtIndex(list, 4);
  return pigeonResult;
}
+ (nullable NVCCacheConfig *)nullableFromList:(NSArray *)list {
  return (list) ? [NVCCacheConfig fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    self.maxCacheSize ?: [NSNull null],
    self.cacheDirectory ?: [NSNull null],
    self.maxConcurrentDownloads ?: [NSNull null],
    self.connectTimeout ?: [NSNull null],
    self.readTimeout ?: [NSNull null],
  ];
}
@end

@implementation NVCCacheInfo
+ (instancetype)makeWithOriginalUrl:(NSString *)originalUrl
    status:(NVCCacheStatus)status
    progress:(double )progress
    cachedSize:(NSInteger )cachedSize
    totalSize:(NSInteger )totalSize
    errorMessage:(nullable NSString *)errorMessage {
  NVCCacheInfo* pigeonResult = [[NVCCacheInfo alloc] init];
  pigeonResult.originalUrl = originalUrl;
  pigeonResult.status = status;
  pigeonResult.progress = progress;
  pigeonResult.cachedSize = cachedSize;
  pigeonResult.totalSize = totalSize;
  pigeonResult.errorMessage = errorMessage;
  return pigeonResult;
}
+ (NVCCacheInfo *)fromList:(NSArray *)list {
  NVCCacheInfo *pigeonResult = [[NVCCacheInfo alloc] init];
  pigeonResult.originalUrl = GetNullableObjectAtIndex(list, 0);
  pigeonResult.status = [GetNullableObjectAtIndex(list, 1) integerValue];
  pigeonResult.progress = [GetNullableObjectAtIndex(list, 2) doubleValue];
  pigeonResult.cachedSize = [GetNullableObjectAtIndex(list, 3) integerValue];
  pigeonResult.totalSize = [GetNullableObjectAtIndex(list, 4) integerValue];
  pigeonResult.errorMessage = GetNullableObjectAtIndex(list, 5);
  return pigeonResult;
}
+ (nullable NVCCacheInfo *)nullableFromList:(NSArray *)list {
  return (list) ? [NVCCacheInfo fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    self.originalUrl ?: [NSNull null],
    @(self.status),
    @(self.progress),
    @(self.cachedSize),
    @(self.totalSize),
    self.errorMessage ?: [NSNull null],
  ];
}
@end

NSObject<FlutterMessageCodec> *NVCNativeVideoCacheCacheListenerGetCodec(void) {
  static FlutterStandardMessageCodec *sSharedObject = nil;
  sSharedObject = [FlutterStandardMessageCodec sharedInstance];
  return sSharedObject;
}

void SetUpNVCNativeVideoCacheCacheListener(id<FlutterBinaryMessenger> binaryMessenger, NSObject<NVCNativeVideoCacheCacheListener> *api) {
  /// 缓存进度变化回调
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.native_video_cache.NativeVideoCacheCacheListener.onCacheProgressChanged"
        binaryMessenger:binaryMessenger
        codec:NVCNativeVideoCacheCacheListenerGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(onCacheProgressChangedUrl:progress:error:)], @"NVCNativeVideoCacheCacheListener api (%@) doesn't respond to @selector(onCacheProgressChangedUrl:progress:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSString *arg_url = GetNullableObjectAtIndex(args, 0);
        double arg_progress = [GetNullableObjectAtIndex(args, 1) doubleValue];
        FlutterError *error;
        [api onCacheProgressChangedUrl:arg_url progress:arg_progress error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  /// 缓存状态变化回调
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.native_video_cache.NativeVideoCacheCacheListener.onCacheStatusChanged"
        binaryMessenger:binaryMessenger
        codec:NVCNativeVideoCacheCacheListenerGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(onCacheStatusChangedUrl:status:error:)], @"NVCNativeVideoCacheCacheListener api (%@) doesn't respond to @selector(onCacheStatusChangedUrl:status:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSString *arg_url = GetNullableObjectAtIndex(args, 0);
        NVCCacheStatus arg_status = [GetNullableObjectAtIndex(args, 1) integerValue];
        FlutterError *error;
        [api onCacheStatusChangedUrl:arg_url status:arg_status error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  /// 缓存错误回调
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.native_video_cache.NativeVideoCacheCacheListener.onCacheError"
        binaryMessenger:binaryMessenger
        codec:NVCNativeVideoCacheCacheListenerGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(onCacheErrorUrl:error:error:)], @"NVCNativeVideoCacheCacheListener api (%@) doesn't respond to @selector(onCacheErrorUrl:error:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSString *arg_url = GetNullableObjectAtIndex(args, 0);
        NSString *arg_error = GetNullableObjectAtIndex(args, 1);
        FlutterError *error;
        [api onCacheErrorUrl:arg_url error:arg_error error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
}
@interface NVCNativeVideoCacheApiCodecReader : FlutterStandardReader
@end
@implementation NVCNativeVideoCacheApiCodecReader
- (nullable id)readValueOfType:(UInt8)type {
  switch (type) {
    case 128: 
      return [NVCCacheConfig fromList:[self readValue]];
    case 129: 
      return [NVCCacheInfo fromList:[self readValue]];
    default:
      return [super readValueOfType:type];
  }
}
@end

@interface NVCNativeVideoCacheApiCodecWriter : FlutterStandardWriter
@end
@implementation NVCNativeVideoCacheApiCodecWriter
- (void)writeValue:(id)value {
  if ([value isKindOfClass:[NVCCacheConfig class]]) {
    [self writeByte:128];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[NVCCacheInfo class]]) {
    [self writeByte:129];
    [self writeValue:[value toList]];
  } else {
    [super writeValue:value];
  }
}
@end

@interface NVCNativeVideoCacheApiCodecReaderWriter : FlutterStandardReaderWriter
@end
@implementation NVCNativeVideoCacheApiCodecReaderWriter
- (FlutterStandardWriter *)writerWithData:(NSMutableData *)data {
  return [[NVCNativeVideoCacheApiCodecWriter alloc] initWithData:data];
}
- (FlutterStandardReader *)readerWithData:(NSData *)data {
  return [[NVCNativeVideoCacheApiCodecReader alloc] initWithData:data];
}
@end

NSObject<FlutterMessageCodec> *NVCNativeVideoCacheApiGetCodec(void) {
  static FlutterStandardMessageCodec *sSharedObject = nil;
  static dispatch_once_t sPred = 0;
  dispatch_once(&sPred, ^{
    NVCNativeVideoCacheApiCodecReaderWriter *readerWriter = [[NVCNativeVideoCacheApiCodecReaderWriter alloc] init];
    sSharedObject = [FlutterStandardMessageCodec codecWithReaderWriter:readerWriter];
  });
  return sSharedObject;
}

void SetUpNVCNativeVideoCacheApi(id<FlutterBinaryMessenger> binaryMessenger, NSObject<NVCNativeVideoCacheApi> *api) {
  /// 初始化缓存系统
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.initialize"
        binaryMessenger:binaryMessenger
        codec:NVCNativeVideoCacheApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(initializeConfig:completion:)], @"NVCNativeVideoCacheApi api (%@) doesn't respond to @selector(initializeConfig:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NVCCacheConfig *arg_config = GetNullableObjectAtIndex(args, 0);
        [api initializeConfig:arg_config completion:^(FlutterError *_Nullable error) {
          callback(wrapResult(nil, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  /// 获取代理URL (用于播放器)
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.getProxyUrl"
        binaryMessenger:binaryMessenger
        codec:NVCNativeVideoCacheApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(getProxyUrlOriginalUrl:completion:)], @"NVCNativeVideoCacheApi api (%@) doesn't respond to @selector(getProxyUrlOriginalUrl:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSString *arg_originalUrl = GetNullableObjectAtIndex(args, 0);
        [api getProxyUrlOriginalUrl:arg_originalUrl completion:^(NSString *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  /// 开始缓存视频
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.startCache"
        binaryMessenger:binaryMessenger
        codec:NVCNativeVideoCacheApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(startCacheUrl:completion:)], @"NVCNativeVideoCacheApi api (%@) doesn't respond to @selector(startCacheUrl:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSString *arg_url = GetNullableObjectAtIndex(args, 0);
        [api startCacheUrl:arg_url completion:^(FlutterError *_Nullable error) {
          callback(wrapResult(nil, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  /// 停止缓存视频
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.stopCache"
        binaryMessenger:binaryMessenger
        codec:NVCNativeVideoCacheApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(stopCacheUrl:completion:)], @"NVCNativeVideoCacheApi api (%@) doesn't respond to @selector(stopCacheUrl:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSString *arg_url = GetNullableObjectAtIndex(args, 0);
        [api stopCacheUrl:arg_url completion:^(FlutterError *_Nullable error) {
          callback(wrapResult(nil, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  /// 获取缓存信息
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.getCacheInfo"
        binaryMessenger:binaryMessenger
        codec:NVCNativeVideoCacheApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(getCacheInfoUrl:completion:)], @"NVCNativeVideoCacheApi api (%@) doesn't respond to @selector(getCacheInfoUrl:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSString *arg_url = GetNullableObjectAtIndex(args, 0);
        [api getCacheInfoUrl:arg_url completion:^(NVCCacheInfo *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  /// 清理所有缓存
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.clearAllCache"
        binaryMessenger:binaryMessenger
        codec:NVCNativeVideoCacheApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(clearAllCacheWithCompletion:)], @"NVCNativeVideoCacheApi api (%@) doesn't respond to @selector(clearAllCacheWithCompletion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        [api clearAllCacheWithCompletion:^(FlutterError *_Nullable error) {
          callback(wrapResult(nil, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  /// 清理指定URL的缓存
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.clearCache"
        binaryMessenger:binaryMessenger
        codec:NVCNativeVideoCacheApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(clearCacheUrl:completion:)], @"NVCNativeVideoCacheApi api (%@) doesn't respond to @selector(clearCacheUrl:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSString *arg_url = GetNullableObjectAtIndex(args, 0);
        [api clearCacheUrl:arg_url completion:^(FlutterError *_Nullable error) {
          callback(wrapResult(nil, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  /// 获取总缓存大小
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.getTotalCacheSize"
        binaryMessenger:binaryMessenger
        codec:NVCNativeVideoCacheApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(getTotalCacheSizeWithCompletion:)], @"NVCNativeVideoCacheApi api (%@) doesn't respond to @selector(getTotalCacheSizeWithCompletion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        [api getTotalCacheSizeWithCompletion:^(NSNumber *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  /// 检查是否已缓存
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.isCached"
        binaryMessenger:binaryMessenger
        codec:NVCNativeVideoCacheApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(isCachedUrl:completion:)], @"NVCNativeVideoCacheApi api (%@) doesn't respond to @selector(isCachedUrl:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSString *arg_url = GetNullableObjectAtIndex(args, 0);
        [api isCachedUrl:arg_url completion:^(NSNumber *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  /// 设置缓存监听器
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.setCacheListener"
        binaryMessenger:binaryMessenger
        codec:NVCNativeVideoCacheApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setCacheListenerWithError:)], @"NVCNativeVideoCacheApi api (%@) doesn't respond to @selector(setCacheListenerWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        [api setCacheListenerWithError:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  /// 移除缓存监听器
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.native_video_cache.NativeVideoCacheApi.removeCacheListener"
        binaryMessenger:binaryMessenger
        codec:NVCNativeVideoCacheApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(removeCacheListenerWithError:)], @"NVCNativeVideoCacheApi api (%@) doesn't respond to @selector(removeCacheListenerWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        [api removeCacheListenerWithError:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
}
