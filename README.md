# Native Video Cache - Flutter 视频缓存插件

Native Video Cache 是一个 Flutter 插件，旨在为 iOS 和 Android 平台提供高效的视频缓存功能。它底层利用了成熟的原生缓存库，并通过 Pigeon 生成的统一 Dart API 层，为开发者提供了一致的调用体验。

## 版本兼容性

| 组件 | 版本要求 |
|------|----------|
| **Flutter** | >= 3.3.0 |
| **Dart** | >= 2.17.0 < 4.0.0 |
| **iOS** | >= 10.0 |
| **Android** | >= API 21 (Android 5.0) |

### 依赖库版本
- **iOS**: KTVHTTPCache (内置)
- **Android**: AndroidVideoCache v2.7.1 (via JitPack)
- **Pigeon**: ^17.1.2

## 特性

*   ✅ 支持 iOS 和 Android 平台
*   ✅ 视频边下边播（流式缓存）
*   ✅ 可配置的缓存大小和缓存策略
*   ✅ 实时获取视频缓存状态和进度
*   ✅ 支持预缓存（后台下载）
*   ✅ 清理指定或所有缓存
*   ✅ 支持HTTP和HTTPS视频源
*   ✅ 多种视频格式支持（MP4、M3U8、HLS等）
*   ✅ 线程安全的并发缓存管理

## 性能特征

### 缓存效率
- **iOS**: 基于KTVHTTPCache，提供精确的字节级进度追踪
- **Android**: 基于AndroidVideoCache，提供高效的代理缓存机制
- **内存占用**: 低内存占用，主要缓存存储在磁盘
- **网络优化**: 支持断点续传和分片下载

### 并发支持
- 默认支持最多3个并发下载任务
- 可通过配置调整并发数量
- 智能队列管理，避免网络拥塞

## 底层实现细节

理解底层原生库的机制有助于更好地使用本插件：

### iOS

*   **核心库**: [`KTVHTTPCache`](https://github.com/ChangbaDevs/KTVHTTPCache)
*   **机制**: `KTVHTTPCache` 是一个功能强大的iOS视频缓存库。它通过在本地启动一个HTTP代理服务器来工作。当播放器请求通过此代理的URL时，`KTVHTTPCache` 会拦截请求，从网络下载数据，将其存储到本地缓存，并同时将数据流式传输给播放器。
*   **优势**:
    *   成熟稳定，广泛应用于多个大型APP。
    *   提供较为精细的缓存控制和状态回调。
    *   支持预加载。
    *   能够较为准确地回报缓存进度（已下载字节数/总字节数）。

### Android

*   **核心库**: [`AndroidVideoCache`](https://github.com/danikula/AndroidVideoCache)
*   **机制**: `AndroidVideoCache` 同样通过在本地运行一个HTTP代理服务器来实现缓存。当应用通过生成的代理URL请求视频时，它会从源URL下载数据，存入缓存，并响应播放器的请求。
*   **优势**:
    *   轻量级且易于集成。
    *   专注于视频的代理缓存和即时播放。
*   **与iOS的主要差异及插件适配**:
    *   **缓存启动**: `AndroidVideoCache` 的缓存是"惰性"的。它在播放器实际请求代理URL时才开始下载。本插件在调用 `startCache()` 时，会主动发起一个对代理URL的HTTP连接来尝试触发缓存。
    *   **进度回报**: `AndroidVideoCache` 的 `CacheListener` 主要通知的是数据块的可用性百分比（相对于当前请求的数据块，而非整个文件）。它不直接提供整个视频文件的总大小或已下载的总字节数。
    *   **插件适配**: 为了在Dart层提供与iOS类似的进度和状态API，本插件的Android端实现会：
        *   通过定期轮询（`ScheduledExecutorService`）来检查视频的缓存状态 (`isCached()`) 和本地缓存文件的大小。
        *   **状态更新**: 基于 `isCached()` 和文件是否存在来判断 `CacheStatus`。
        *   **进度更新**: 由于缺乏直接的总文件大小信息，Android端的缓存进度是基于**估算**的。它主要通过观察缓存文件大小的变化来模拟进度百分比。因此，其进度更新可能不如iOS端精确和实时。在视频完全缓存（`isCached()`返回true）之前，进度主要反映的是"正在积极下载中"，而非精确的百分比。

## 统一接口层 (Pigeon)

本插件使用 Google 的 [Pigeon](https://pub.dev/packages/pigeon) 工具来定义平台无关的Dart API (`pigeons/api.dart`)，并自动生成iOS (Objective-C) 和 Android (Java) 的原生绑定代码。这使得开发者可以在Dart中调用统一的接口，而插件内部则处理与不同原生库的交互和适配。

开发者主要与 `lib/src/native_video_cache_manager.dart` 提供的API进行交互。

## 如何更新Pigeon绑定代码

如果 `pigeons/api.dart` 文件中的接口定义发生了变更（例如，添加新方法、修改参数等），你需要重新生成所有平台的绑定代码。在插件的根目录下运行以下命令：

```bash
flutter clean
flutter pub get
dart run pigeon --input pigeons/api.dart
```

这将更新 `lib/src/generated/`、`ios/Classes/` 和 `android/src/main/java/.../` 目录下的相关Pigeon生成文件。

## 快速开始

### 第一步：添加依赖

#### 方式一：从GitHub引用（推荐）

在你的 `pubspec.yaml` 文件中添加插件依赖：

```yaml
dependencies:
  native_video_cache:
    git:
      url: https://github.com/YOUR_USERNAME/YOUR_REPOSITORY_NAME.git
      path: packages/native_video_cache
```

#### 方式二：本地路径引用（开发阶段）

如果你在本地开发或测试：

```yaml
dependencies:
  native_video_cache:
    path: ../path/to/native_video_cache
```

#### 方式三：pub.dev版本引用（待发布）

```yaml
# 注意：此插件尚未发布到pub.dev
# 发布后可使用以下方式：
dependencies:
  native_video_cache: ^0.0.1
```

然后运行：
```bash
flutter pub get
```

### 第二步：平台配置

参考下面的详细配置说明完成Android和iOS的配置。

### 第三步：初始化并使用

```dart
import 'package:native_video_cache/native_video_cache.dart';

// 初始化
await NativeVideoCacheManager.initialize(
  CacheConfig(maxCacheSize: 1024 * 1024 * 1024), // 1GB
);

// 获取代理URL
String proxyUrl = await NativeVideoCacheManager.getProxyUrl(originalUrl);

// 用于播放器
VideoPlayerController.network(proxyUrl);
```

## 使用方法

### 1. Android 配置 (重要!)

由于 `AndroidVideoCache` 通过本地HTTP代理工作，如果你的视频源是HTTP，或者为了确保本地代理正常通信，你需要在你的Android应用的 `AndroidManifest.xml` 文件中允许明文HTTP流量。

打开 `example/android/app/src/main/AndroidManifest.xml` (或你主项目的相应文件)，在 `<application>` 标签中添加 `android:usesCleartextTraffic="true"` 属性：

```xml
<manifest ...>
    <application
        ...
        android:usesCleartextTraffic="true">
        ...
    </application>
</manifest>
```

#### 1.1. Flutter 项目集成插件时，请在项目的android 目录下的 build.gradle 和 settings.gradle 文件添加如下 maven，否则会找不到依赖库 

```
// build.gradle

allprojects {
    repositories {
+        maven { url "https://jitpack.io" }
+        maven { url 'https://maven.aliyun.com/repository/public' }
        google()
        mavenCentral()
    }
}
...
```
```
// settings.gradle

pluginManagement {
    ...
    repositories {
+        maven { url "https://jitpack.io" }
+        maven { url 'https://maven.aliyun.com/repository/public' }
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}
...
```

### 2. 初始化缓存系统

在使用任何缓存功能之前，需要先初始化。建议在应用启动时进行。

```dart
import 'package:native_video_cache/native_video_cache.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized(); // 确保Flutter绑定已初始化
  await _initializeCache();
  runApp(MyApp());
}

Future<void> _initializeCache() async {
  try {
    // 可选配置
    final config = CacheConfig(
      maxCacheSize: 1024 * 1024 * 1024, // 1GB
      // cacheDirectory: '/path/to/custom/cache', // 自定义缓存目录 (通常不需要)
      connectTimeout: 30 * 1000, // 30秒连接超时 (仅iOS部分生效，Android有其内部处理)
      readTimeout: 30 * 1000,    // 30秒读取超时 (仅iOS部分生效)
    );
    await NativeVideoCacheManager.initialize(config);
    print('Native Video Cache: 缓存系统初始化成功');
  } catch (e) {
    print('Native Video Cache: 缓存系统初始化失败: $e');
  }
}
```

### 3. 获取代理URL并用于播放器

要播放并缓存视频，首先获取其代理URL，然后将此URL传递给你的视频播放器。

```dart
String originalUrl = "YOUR_VIDEO_URL_HERE";
String? proxyUrl;

try {
  proxyUrl = await NativeVideoCacheManager.getProxyUrl(originalUrl);
  if (proxyUrl != null) {
    print('Native Video Cache: 获取到代理URL: $proxyUrl');
    // 现在可以将 proxyUrl 用于你的视频播放器 (例如 video_player, chewie)
    // _videoPlayerController = VideoPlayerController.network(proxyUrl);
    // await _videoPlayerController.initialize();
    // setState(() {});
  } else {
    print('Native Video Cache: 获取代理URL失败，可能原始URL无效或缓存系统未初始化');
  }
} catch (e) {
  print('Native Video Cache: 获取代理URL时发生错误: $e');
}
```

### 4. (可选) 主动开始缓存

如果你想在播放前就开始缓存视频（预缓存），可以调用 `startCache`。

```dart
try {
  await NativeVideoCacheManager.startCache(originalUrl);
  print('Native Video Cache: 已请求开始缓存: $originalUrl');
} catch (e) {
  print('Native Video Cache: 请求开始缓存时发生错误: $e');
}
```
**注意**: 在Android上，`startCache` 会尝试通过主动请求代理URL来触发缓存。缓存的实际开始仍依赖于 `AndroidVideoCache` 的惰性机制。

### 5. 监听缓存状态和进度

你可以注册监听器来接收特定URL的缓存状态和进度更新。

```dart
void _listenToCacheEvents(String videoUrl) {
  // 添加状态监听器
  NativeVideoCacheManager.addStatusListener((url, status) {
    if (url == videoUrl) {
      print('Native Video Cache: 状态变更 [$url]: $status');
      // setState(() { _cacheStatus = status; });
    }
  });

  // 添加进度监听器
  NativeVideoCacheManager.addProgressListener((url, progress) {
    if (url == videoUrl) {
      print('Native Video Cache: 进度变更 [$url]: $progress');
      // setState(() { _cacheProgress = progress; });
    }
  });
}

// 不要忘记在dispose时清理监听器
// @override
// void dispose() {
//   NativeVideoCacheManager.clearAllListeners(); // 清理所有监听器
//   super.dispose();
// }
```

### 6. 获取缓存信息

查询特定URL的缓存信息。

```dart
try {
  CacheInfo cacheInfo = await NativeVideoCacheManager.getCacheInfo(originalUrl);
  print('Native Video Cache: 缓存信息 [${cacheInfo.originalUrl}]:');
  print('  状态: ${cacheInfo.status}');
  print('  进度: ${cacheInfo.progress}');
  print('  已缓存大小: ${cacheInfo.cachedSize} bytes');
  print('  总大小: ${cacheInfo.totalSize} bytes'); // 注意: Android端totalSize可能不准确
} catch (e) {
  print('Native Video Cache: 获取缓存信息时发生错误: $e');
}
```

### 7. 检查视频是否已缓存

```dart
try {
  bool isCached = await NativeVideoCacheManager.isCached(originalUrl);
  print('Native Video Cache: $originalUrl 是否已缓存: $isCached');
} catch (e) {
  print('Native Video Cache: 检查缓存状态时发生错误: $e');
}
```

### 8. 清理缓存

*   清理指定URL的缓存:
    ```dart
    try {
      await NativeVideoCacheManager.clearCache(originalUrl);
      print('Native Video Cache: 已清理 $originalUrl 的缓存');
    } catch (e) {
      print('Native Video Cache: 清理指定缓存时发生错误: $e');
    }
    ```
*   清理所有缓存:
    ```dart
    try {
      await NativeVideoCacheManager.clearAllCache();
      print('Native Video Cache: 已清理所有缓存');
    } catch (e) {
      print('Native Video Cache: 清理所有缓存时发生错误: $e');
    }
    ```

### 9. 获取总缓存大小

```dart
try {
  int totalCacheSize = await NativeVideoCacheManager.getTotalCacheSize();
  print('Native Video Cache: 当前总缓存大小: $totalCacheSize bytes');
} catch (e) {
  print('Native Video Cache: 获取总缓存大小时发生错误: $e');
}
```

## 最佳实践

### 缓存策略建议
```dart
// 推荐的缓存配置
final config = CacheConfig(
  maxCacheSize: 2 * 1024 * 1024 * 1024, // 2GB，适合大多数应用
  maxConcurrentDownloads: 3, // 限制并发下载数量
  connectTimeout: 15 * 1000, // 15秒连接超时
  readTimeout: 30 * 1000,    // 30秒读取超时
);
```

### 生命周期管理
```dart
class VideoPlayerPage extends StatefulWidget {
  @override
  _VideoPlayerPageState createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> {
  @override
  void initState() {
    super.initState();
    _setupCacheListeners();
  }

  @override
  void dispose() {
    // 重要：清理监听器避免内存泄漏
    NativeVideoCacheManager.clearAllListeners();
    super.dispose();
  }

  void _setupCacheListeners() {
    NativeVideoCacheManager.addProgressListener((url, progress) {
      // 处理进度更新
    });
    
    NativeVideoCacheManager.addErrorListener((url, error) {
      // 处理缓存错误
    });
  }
}
```

### 预缓存策略
```dart
// 在WiFi环境下预缓存热门视频
Future<void> preloadPopularVideos(List<String> urls) async {
  for (String url in urls) {
    try {
      await NativeVideoCacheManager.startCache(url);
      // 可以添加延迟避免网络拥塞
      await Future.delayed(Duration(seconds: 1));
    } catch (e) {
      print('预缓存失败: $url, 错误: $e');
    }
  }
}
```

## 高级功能：全功能组合使用

对于企业级应用或需要完整监控和管理功能的场景，本插件提供了一套增强管理器，可以与核心缓存功能配合使用。

### 增强管理器介绍

除了核心的 `NativeVideoCacheManager`，插件还提供了以下可选的增强管理器：

- **AppLifecycleManager**: APP生命周期管理，处理前后台切换时的缓存策略
- **CacheMemoryManager**: 内存管理，LRU策略防止内存泄漏
- **CacheStabilityManager**: 稳定性管理，重试机制和异常恢复
- **CachePerformanceMonitor**: 性能监控，追踪缓存指标和分析

### 全功能组合示例

```dart
import 'package:flutter/material.dart';
import 'package:native_video_cache/native_video_cache.dart';
// 注意：以下import需要在插件中添加相应的管理器文件
import 'package:native_video_cache/src/app_lifecycle_manager.dart';
import 'package:native_video_cache/src/cache_memory_manager.dart';
import 'package:native_video_cache/src/cache_stability_manager.dart';
import 'package:native_video_cache/src/cache_performance_monitor.dart';
import 'dart:async';

/// 企业级视频缓存应用示例
/// 集成所有增强管理器，提供完整的监控和管理功能
class EnterpriseVideoApp extends StatefulWidget {
  @override
  _EnterpriseVideoAppState createState() => _EnterpriseVideoAppState();
}

class _EnterpriseVideoAppState extends State<EnterpriseVideoApp> 
    with WidgetsBindingObserver {
  Timer? _reportTimer;
  List<String> _videoUrls = [];
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeFullStack();
    _startPerformanceReporting();
  }

  /// 初始化完整的缓存管理栈
  Future<void> _initializeFullStack() async {
    try {
      // 1. 核心缓存系统初始化
      await NativeVideoCacheManager.initialize(
        CacheConfig(
          maxCacheSize: 2048 * 1024 * 1024, // 2GB
          cacheDirectory: 'enterprise_video_cache',
          connectTimeout: 15 * 1000,
          readTimeout: 30 * 1000,
        ),
      );

      // 2. APP生命周期管理初始化
      AppLifecycleManager.instance.initialize();

      // 3. 内存管理器初始化
      CacheMemoryManager.instance.initialize(
        maxUrls: 200,                    // 最多管理200个URL状态
        maxMemoryUsage: 100 * 1024 * 1024, // 最大100MB内存使用
      );

      // 4. 稳定性管理器初始化
      CacheStabilityManager.instance.initialize(
        maxRetries: 5,                   // 最多重试5次
        retryDelay: Duration(seconds: 3), // 重试间隔3秒
      );

      // 5. 性能监控器初始化
      CachePerformanceMonitor.instance.initialize();

      setState(() {
        _isInitialized = true;
      });

      print('🎉 企业级缓存系统初始化完成');
    } catch (e) {
      print('❌ 缓存系统初始化失败: $e');
    }
  }

  /// 企业级视频缓存方法
  /// 集成所有增强功能：重试机制、性能监控、内存管理等
  Future<void> _enterpriseCacheVideo(String url) async {
    if (!_isInitialized) {
      print('⚠️ 系统未初始化');
      return;
    }

    try {
      // 记录缓存开始事件
      CachePerformanceMonitor.instance.recordCacheStart(url);
      
      // 使用稳定性管理器缓存（包含自动重试机制）
      await CacheStabilityManager.instance.cacheWithRetry(url);
      
      // 记录缓存成功
      CachePerformanceMonitor.instance.recordCacheComplete(url);
      
      print('✅ 缓存成功: $url');
      
    } catch (e) {
      // 记录缓存失败
      CachePerformanceMonitor.instance.recordCacheFailure(url, e.toString());
      print('❌ 缓存失败: $url - $e');
      rethrow;
    }
  }

  /// 批量缓存视频
  Future<void> _batchCacheVideos(List<String> urls) async {
    for (String url in urls) {
      await _enterpriseCacheVideo(url);
      // 避免网络拥塞，添加短暂延迟
      await Future.delayed(Duration(milliseconds: 500));
    }
  }

  /// 启动性能报告定时器
  void _startPerformanceReporting() {
    // 每分钟生成一次综合报告
    _reportTimer = Timer.periodic(Duration(minutes: 1), (_) {
      _generateComprehensiveReport();
    });
  }

  /// 生成综合系统报告
  void _generateComprehensiveReport() {
    if (!_isInitialized) return;

    // 获取各管理器的报告
    final performanceReport = CachePerformanceMonitor.instance.getPerformanceReport();
    final memoryReport = CacheMemoryManager.instance.getMemoryReport();
    final healthReport = CacheStabilityManager.instance.getHealthReport();

    // 综合分析
    print('📊 === 企业级缓存系统报告 ===');
    print('🎯 性能指标:');
    print('   - 总缓存任务: ${performanceReport['totalUrls']}');
    print('   - 成功率: ${(performanceReport['overallSuccessRate'] * 100).toStringAsFixed(1)}%');
    print('   - 平均耗时: ${performanceReport['averageDuration']?.toStringAsFixed(1) ?? 'N/A'}秒');
    
    print('💾 内存使用:');
    print('   - 活跃URL: ${memoryReport['activeUrls']}');
    print('   - 内存占用: ${_formatBytes(memoryReport['memoryUsage'])}');
    print('   - LRU缓存: ${memoryReport['lruCacheSize']}');
    
    print('🔄 系统健康:');
    print('   - 成功任务: ${healthReport['successfulTasks']}');
    print('   - 失败任务: ${healthReport['failedTasks']}');
    print('   - 重试任务: ${healthReport['retriedTasks']}');
    
    // 健康状况评估
    final successRate = performanceReport['overallSuccessRate'] as double;
    final failureRate = healthReport['failureRate'] as double;
    
    if (successRate < 0.8) {
      print('⚠️ 警告: 缓存成功率低于80%，请检查网络环境');
    }
    
    if (failureRate > 0.3) {
      print('🚨 严重: 缓存失败率超过30%，建议检查服务器状态');
    }
    
    print('================================');

    // 可选：发送数据到分析服务器
    _sendToAnalyticsServer({
      'performance': performanceReport,
      'memory': memoryReport,
      'health': healthReport,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// 发送分析数据到服务器（示例）
  void _sendToAnalyticsServer(Map<String, dynamic> data) {
    // 在实际应用中，这里会发送到企业分析平台
    print('📡 发送分析数据到服务器: ${data.keys.join(', ')}');
    // 例如：
    // await httpClient.post('/api/cache-analytics', body: jsonEncode(data));
  }

  /// 格式化字节数显示
  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// 手动触发系统健康检查
  void _performHealthCheck() {
    if (!_isInitialized) return;

    print('🔍 执行系统健康检查...');
    
    // 检查内存使用情况
    final memoryReport = CacheMemoryManager.instance.getMemoryReport();
    final memoryUsagePercent = (memoryReport['memoryUsage'] as int) / (100 * 1024 * 1024);
    
    if (memoryUsagePercent > 0.8) {
      print('⚠️ 内存使用率超过80%，建议清理部分缓存');
      CacheMemoryManager.instance.cleanup();
    }
    
    // 检查稳定性状况
    final healthReport = CacheStabilityManager.instance.getHealthReport();
    final failureRate = healthReport['failureRate'] as double;
    
    if (failureRate > 0.5) {
      print('🚨 缓存失败率过高，可能存在网络问题');
    }
    
    // 检查性能指标
    final performanceReport = CachePerformanceMonitor.instance.getPerformanceReport();
    final recentEvents = CachePerformanceMonitor.instance.getRecentEvents(limit: 10);
    
    print('✅ 健康检查完成，最近${recentEvents.length}个事件');
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '企业级视频缓存',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: Scaffold(
        appBar: AppBar(
          title: Text('Enterprise Video Cache'),
          actions: [
            IconButton(
              icon: Icon(Icons.health_and_safety),
              onPressed: _performHealthCheck,
              tooltip: '健康检查',
            ),
          ],
        ),
        body: _isInitialized 
            ? _buildMainContent()
            : Center(child: CircularProgressIndicator()),
      ),
    );
  }

  Widget _buildMainContent() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 系统状态卡片
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('🎯 系统状态', style: Theme.of(context).textTheme.headlineSmall),
                  SizedBox(height: 8),
                  Text('✅ 企业级缓存系统已就绪'),
                  Text('🔧 包含: 生命周期管理、内存管理、稳定性管理、性能监控'),
                ],
              ),
            ),
          ),
          
          SizedBox(height: 16),
          
          // 操作按钮
          ElevatedButton(
            onPressed: () async {
              // 示例：缓存热门视频列表
              final sampleUrls = [
                'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
                'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4',
              ];
              await _batchCacheVideos(sampleUrls);
            },
            child: Text('批量缓存示例视频'),
          ),
          
          SizedBox(height: 8),
          
          ElevatedButton(
            onPressed: _generateComprehensiveReport,
            child: Text('生成即时报告'),
          ),
          
          SizedBox(height: 16),
          
          // 说明文本
          Expanded(
            child: SingleChildScrollView(
              child: Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('📋 功能说明', style: Theme.of(context).textTheme.headlineSmall),
                      SizedBox(height: 12),
                      _buildFeatureItem('🔄', '自动重试机制', '网络异常时自动重试，提高成功率'),
                      _buildFeatureItem('💾', 'LRU内存管理', '防止内存泄漏，自动清理旧缓存状态'),
                      _buildFeatureItem('📱', '生命周期优化', '后台时暂停缓存，前台时恢复'),
                      _buildFeatureItem('📊', '性能监控', '实时跟踪缓存性能，生成分析报告'),
                      _buildFeatureItem('🔍', '健康检查', '监控系统状态，预警潜在问题'),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(String icon, String title, String description) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(icon, style: TextStyle(fontSize: 20)),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: TextStyle(fontWeight: FontWeight.bold)),
                Text(description, style: TextStyle(color: Colors.grey[600])),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    // 按相反顺序释放资源
    _reportTimer?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    
    if (_isInitialized) {
      CachePerformanceMonitor.instance.dispose();
      CacheStabilityManager.instance.dispose();
      CacheMemoryManager.instance.dispose();
      AppLifecycleManager.instance.dispose();
      NativeVideoCacheManager.dispose();
    }
    
    super.dispose();
  }
}

/// 应用入口点
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(EnterpriseVideoApp());
}
```

### 增强功能优势

**🎯 企业级特性:**
- **完整监控**: 性能指标、内存使用、系统健康状况
- **自动化管理**: 生命周期优化、内存清理、异常恢复
- **高可靠性**: 自动重试、故障恢复、状态监控
- **便于调试**: 详细日志、性能报告、健康检查

**📊 监控数据:**
- 缓存成功率和失败率
- 平均缓存时间和网络速度
- 内存使用情况和LRU缓存状态
- 重试次数和异常恢复情况

**🔧 自动化功能:**
- APP进入后台时自动暂停非关键缓存
- 内存不足时自动清理最旧的缓存状态
- 网络异常时自动重试和故障恢复
- 定期生成性能报告和健康检查

这个全功能组合特别适合需要高可靠性和完整监控的企业级应用。对于简单应用，可以只使用核心的 `NativeVideoCacheManager`。

## 故障排除

### 常见问题

#### 1. Android构建失败
**问题**: 找不到AndroidVideoCache依赖
**解决方案**: 确保在 `build.gradle` 和 `settings.gradle` 中添加了JitPack仓库：
```gradle
maven { url "https://jitpack.io" }
```

#### 2. iOS播放异常
**问题**: 视频无法播放或缓存失败
**解决方案**: 
- 检查`Info.plist`中是否允许HTTP流量
- 确保视频URL可访问
- 检查网络权限

#### 3. 缓存进度不准确
**问题**: Android端进度显示异常
**解决方案**: 这是正常现象，Android端进度为估算值，建议以缓存状态为准

#### 4. 内存泄漏
**问题**: 应用内存持续增长
**解决方案**: 确保在Widget dispose时调用 `clearAllListeners()`

### 调试技巧

#### 启用详细日志
```dart
// 在开发环境启用详细日志
if (kDebugMode) {
  // 监听所有缓存事件用于调试
  NativeVideoCacheManager.addProgressListener((url, progress) {
    debugPrint('缓存进度: $url -> $progress');
  });
  
  NativeVideoCacheManager.addStatusListener((url, status) {
    debugPrint('缓存状态: $url -> $status');
  });
  
  NativeVideoCacheManager.addErrorListener((url, error) {
    debugPrint('缓存错误: $url -> $error');
  });
}
```

#### 检查缓存状态
```dart
Future<void> debugCacheStatus(String url) async {
  try {
    final info = await NativeVideoCacheManager.getCacheInfo(url);
    print('=== 缓存调试信息 ===');
    print('URL: ${info.originalUrl}');
    print('状态: ${info.status}');
    print('进度: ${(info.progress * 100).toStringAsFixed(1)}%');
    print('已缓存: ${_formatBytes(info.cachedSize)}');
    print('总大小: ${_formatBytes(info.totalSize)}');
    print('================');
  } catch (e) {
    print('获取缓存信息失败: $e');
  }
}

String _formatBytes(int bytes) {
  if (bytes < 1024) return '$bytes B';
  if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
  if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
}
```

## 限制和已知问题

### 平台差异
- **Android**: 缓存进度为估算值，可能不够精确
- **iOS**: 在某些网络环境下可能出现连接超时
- **通用**: HLS直播流不支持缓存（仅支持点播）

### 性能限制
- 建议单个视频文件不超过2GB
- 并发下载数量建议不超过5个
- 在低性能设备上建议降低并发数量

### 网络要求
- 需要稳定的网络连接
- 支持HTTP/HTTPS协议
- 某些CDN可能有防盗链限制

## 示例项目

本插件包含完整的示例项目，位于 `example/` 目录：

```bash
cd example
flutter run
```

示例项目包含：
- 基础缓存功能演示
- 视频播放器集成
- 缓存状态监听
- 批量预缓存
- 缓存管理界面

## 注意事项

*   **Android进度估算**: 如前所述，Android端的缓存进度是估算的。在视频完全下载之前，它更多地表示"正在下载"的状态，而不是精确的百分比。
*   **错误处理**: 请务必在调用插件API时使用 `try-catch`块来处理潜在的异常。
*   **生命周期管理**: 确保在不需要监听缓存事件时（例如，Widget被dispose时）取消 `StreamSubscription` 并调用 `NativeVideoCacheManager.clearAllListeners()`。
*   **网络权限**: 确保你的应用拥有访问网络的权限 (通常 Flutter 项目默认包含)。
    *   iOS: `Info.plist`
    *   Android: `AndroidManifest.xml` (`<uses-permission android:name="android.permission.INTERNET" />`)
*   **存储权限**: 如果指定自定义缓存目录，确保应用有相应的存储权限。
*   **HTTPS支持**: 插件完全支持HTTPS视频源，建议在生产环境使用HTTPS。

## 更新日志

### v0.0.1 (初始版本)
- ✅ 基础视频缓存功能
- ✅ iOS和Android平台支持
- ✅ 统一的Dart API
- ✅ 缓存状态和进度监听
- ✅ 示例应用

## 贡献

我们欢迎社区贡献！如果你发现问题或有改进建议：

1. 提交Issue描述问题
2. Fork项目并创建特性分支
3. 提交Pull Request

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 支持

如果这个插件对你有帮助，请给我们一个⭐️！

有问题或建议？请在GitHub上提交Issue。

## 开发者指南

### 发布前准备清单

在发布到GitHub或pub.dev之前，请确保：

#### 必要的文件检查
- ✅ `README.md` - 完整的说明文档（已完成）
- ✅ `LICENSE` - 开源许可证文件
- ✅ `CHANGELOG.md` - 版本更新记录
- ✅ `pubspec.yaml` - 正确的插件配置

#### pubspec.yaml 配置检查
```yaml
name: native_video_cache
description: Flutter视频缓存插件，基于成熟的原生库实现高效视频缓存
version: 0.0.1
homepage: https://github.com/YOUR_USERNAME/YOUR_REPOSITORY_NAME
repository: https://github.com/YOUR_USERNAME/YOUR_REPOSITORY_NAME
# 发布到pub.dev时移除下面这行
# publish_to: none

environment:
  sdk: ">=2.17.0 <4.0.0"
  flutter: ">=3.3.0"
```

#### 代码质量检查
```bash
# 在插件根目录运行
flutter analyze                    # 静态分析
flutter test                      # 运行测试
flutter pub publish --dry-run     # 预检查发布
```

### 发布到GitHub

1. **创建GitHub仓库**
```bash
git init
git add .
git commit -m "Initial commit: Native Video Cache plugin"
git remote add origin https://github.com/YOUR_USERNAME/YOUR_REPOSITORY_NAME.git
git push -u origin main
```

2. **更新README中的Git URL**
   将 `YOUR_USERNAME` 和 `YOUR_REPOSITORY_NAME` 替换为实际的GitHub用户名和仓库名。

3. **创建Release Tag**
```bash
git tag v0.0.1
git push origin v0.0.1
```

### 发布到pub.dev（可选）

如果要发布到pub.dev，需要：

1. **完善插件信息**
   - 确保 `pubspec.yaml` 中的 `homepage` 和 `repository` 字段正确
   - 添加详细的 `description`
   - 移除 `publish_to: none`

2. **发布命令**
```bash
cd packages/native_video_cache
flutter pub publish --dry-run  # 预检查
flutter pub publish            # 正式发布
```

### 版本管理建议

```yaml
# pubspec.yaml 版本号规范
version: 0.0.1+1
#        ^^^^^  ^
#        |      |
#        |      +-- build number (构建号)
#        +--------- version (版本号: major.minor.patch)
```

## 注意事项
