import 'package:flutter_test/flutter_test.dart';
import 'package:native_video_cache/native_video_cache.dart';
import 'package:native_video_cache/native_video_cache_platform_interface.dart';
import 'package:native_video_cache/native_video_cache_method_channel.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockNativeVideoCachePlatform
    with MockPlatformInterfaceMixin
    implements NativeVideoCachePlatform {
  @override
  Future<String?> getPlatformVersion() => Future.value('42');
}

void main() {
  final NativeVideoCachePlatform initialPlatform = NativeVideoCachePlatform.instance;

  test('$MethodChannelNativeVideoCache is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelNativeVideoCache>());
  });

  // test('getPlatformVersion', () async {
  //   NativeVideoCache nativeAvPlugin = NativeVideoCache();
  //   MockNativeVideoCachePlatform fakePlatform = MockNativeVideoCachePlatform();
  //   NativeVideoCachePlatform.instance = fakePlatform;

  //   expect(await nativeAvPlugin.getPlatformVersion(), '42');
  // });

  group('NativeVideoCacheManager', () {
    test('formatFileSize formats bytes correctly', () {
      expect(NativeVideoCacheManager.formatFileSize(512), '512 B');
      expect(NativeVideoCacheManager.formatFileSize(1024), '1.0 KB');
      expect(NativeVideoCacheManager.formatFileSize(1536), '1.5 KB');
      expect(NativeVideoCacheManager.formatFileSize(1024 * 1024), '1.0 MB');
      expect(NativeVideoCacheManager.formatFileSize(1024 * 1024 * 1024), '1.0 GB');
    });

    test('CacheConfig can be created with default values', () {
      final config = CacheConfig();
      expect(config.maxCacheSize, isNull);
      expect(config.cacheDirectory, isNull);
      expect(config.maxConcurrentDownloads, isNull);
      expect(config.connectTimeout, isNull);
      expect(config.readTimeout, isNull);
    });

    test('CacheConfig can be created with custom values', () {
      final config = CacheConfig(
        maxCacheSize: 1024 * 1024 * 1024,
        cacheDirectory: '/custom/path',
        maxConcurrentDownloads: 5,
        connectTimeout: 60000,
        readTimeout: 60000,
      );

      expect(config.maxCacheSize, 1024 * 1024 * 1024);
      expect(config.cacheDirectory, '/custom/path');
      expect(config.maxConcurrentDownloads, 5);
      expect(config.connectTimeout, 60000);
      expect(config.readTimeout, 60000);
    });
  });
}
