import 'package:pigeon/pigeon.dart';

@ConfigurePigeon(PigeonOptions(
  dartOut: 'lib/src/generated/native_video_cache_api.g.dart',
  dartOptions: DartOptions(),
  cppHeaderOut: 'windows/native_video_cache_api.h',
  cppSourceOut: 'windows/native_video_cache_api.cpp',
  cppOptions: CppOptions(namespace: 'native_video_cache'),
  objcHeaderOut: 'ios/Classes/NativeVideoCacheApi.h',
  objcSourceOut: 'ios/Classes/NativeVideoCacheApi.m',
  objcOptions: ObjcOptions(prefix: 'NVC'),
  javaOut:
      'android/src/main/java/com/native_video_cache/native_video_cache/NativeVideoCacheApiGenerated.java',
  javaOptions: JavaOptions(
      package: 'com.native_video_cache.native_video_cache',
      className: 'NativeVideoCacheApiGenerated'),
  kotlinOut:
      'android/src/main/java/com/native_video_cache/native_video_cache/NativeVideoCacheApi.kt',
  kotlinOptions:
      KotlinOptions(package: 'com.native_video_cache.native_video_cache'),
))

/// 缓存配置类
class CacheConfig {
  /// 最大缓存大小 (bytes)
  final int? maxCacheSize;

  /// 缓存目录路径
  final String? cacheDirectory;

  /// 最大并发下载数
  final int? maxConcurrentDownloads;

  /// 连接超时时间 (milliseconds)
  final int? connectTimeout;

  /// 读取超时时间 (milliseconds)
  final int? readTimeout;

  CacheConfig({
    this.maxCacheSize,
    this.cacheDirectory,
    this.maxConcurrentDownloads,
    this.connectTimeout,
    this.readTimeout,
  });
}

/// 缓存状态枚举
enum CacheStatus {
  /// 未缓存
  none,

  /// 缓存中
  caching,

  /// 已缓存
  cached,

  /// 缓存错误
  error,
}

/// 缓存信息类
class CacheInfo {
  /// 原始URL
  final String originalUrl;

  /// 缓存状态
  final CacheStatus status;

  /// 缓存进度 (0.0 - 1.0)
  final double progress;

  /// 已缓存大小 (bytes)
  final int cachedSize;

  /// 总大小 (bytes)
  final int totalSize;

  /// 错误信息
  final String? errorMessage;

  CacheInfo({
    required this.originalUrl,
    required this.status,
    required this.progress,
    required this.cachedSize,
    required this.totalSize,
    this.errorMessage,
  });
}

/// 缓存监听回调
@HostApi()
abstract class NativeVideoCacheCacheListener {
  /// 缓存进度变化回调
  void onCacheProgressChanged(String url, double progress);

  /// 缓存状态变化回调
  void onCacheStatusChanged(String url, CacheStatus status);

  /// 缓存错误回调
  void onCacheError(String url, String error);
}

/// Native Video Cache API
@HostApi()
abstract class NativeVideoCacheApi {
  /// 初始化缓存系统
  @async
  void initialize(CacheConfig config);

  /// 获取代理URL (用于播放器)
  @async
  String getProxyUrl(String originalUrl);

  /// 开始缓存视频
  @async
  void startCache(String url);

  /// 停止缓存视频
  @async
  void stopCache(String url);

  /// 获取缓存信息
  @async
  CacheInfo getCacheInfo(String url);

  /// 清理所有缓存
  @async
  void clearAllCache();

  /// 清理指定URL的缓存
  @async
  void clearCache(String url);

  /// 获取总缓存大小
  @async
  int getTotalCacheSize();

  /// 检查是否已缓存
  @async
  bool isCached(String url);

  /// 设置缓存监听器
  void setCacheListener();

  /// 移除缓存监听器
  void removeCacheListener();
}
